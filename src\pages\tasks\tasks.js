import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Modal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useLocation, useNavigate } from "react-router-dom";
import { AdmminAprovaple, CreatingType } from "../../constant/constants";
import { tasksAPis } from "../../apis/tasks/api";
import { allTasksQueries } from "../../apis/tasks/query";
import { useForm } from "react-hook-form";
import { delegateQueries } from "../../apis/delegate/query";
import CustomSelect from "../../components/Common/Select";
import {
  formatDate,
  formatDateLocalTime,
  formatISOToCustomDate,
  getReasons<PERSON>ey<PERSON>yValue,
  handleBackendErrors,
  hasPermission,
  today,
} from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import DeleteModal from "../../components/Common/DeleteModal";
import TypesModel from "../../components/Common/types-model";
import ActionTask from "./action-tasks";
import TextAreaField from "../../components/Common/textArea";
import CustomInput from "../../components/Common/Input";
import SearchCard from "../../components/Reports/search-card";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
import { clientsQueries } from "../../apis/clients/query";
import { reasonsTypesQueries } from "../../apis/types/resons/query";
import { ReasonsEnum } from "../../constant/constants";
import { MdCancel, MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
import { HiDotsVertical } from "react-icons/hi";
import { GrStatusGood } from "react-icons/gr";
import { BiTransfer } from "react-icons/bi";
import { IoCheckmarkDoneCircle } from "react-icons/io5";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import "./index.css"; // للتنسيق

const Tasks = () => {
  const [pagination, setPagination] = useState(1);
  const [searchParams, setSearchParams] = useState({});
  const [dateError, setDateError] = useState(null);
  const { pathname, search } = useLocation();
  const navigate = useNavigate();
  const queryParams = useMemo(() => new URLSearchParams(search), [search]);

  const { data, isLoading, refetch } = allTasksQueries.useGetAll({
    limit: 50,
    page: pagination,
    searchParams,
  });

  const { t, i18n } = useTranslation();
  const [selectId, setSelectId] = useState(null);
  const [selectedDelegate, setSelectedDelegate] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openCancelModal, setOpenCancelModal] = useState(false);
  const [openTransferList, setOpenTransfereList] = useState(false);
  const [openApproveModel, setOpenApproveModel] = useState(false);
  const [openMenu, setOpenMenu] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedCreatingType, setSelectedCteatingType] = useState(null);
  const [isShow, setIsShow] = useState(false);
  const [transactionId, setTransactionId] = useState(false);
  const [open, setOpen] = useState(false);
  const [openDone, setOpenDone] = useState(false);

  // Add effect to watch language changes
  useEffect(() => {
    refetch();
  }, [i18n.language]);

  // Apply default date filter on initial load
  useEffect(() => {
    // Only apply default filter if no search params exist and no URL params
    if (Object.keys(searchParams).length === 0 && !search) {
      const todayFormatted = formatDate(today);
      const defaultParams = {
        "task_date[from]": todayFormatted,
        "task_date[to]": todayFormatted,
      };
      setSearchParams(defaultParams);
    }
  }, []);

  // Fetch filter data
  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const {
    control,
    watch,
    register,
    setValue,
    reset,
    formState: { errors, isSubmitting },
    handleSubmit,
    setError,
    clearErrors,
  } = useForm({
    defaultValues: {
      delegate_id: null,
      filter_delegate_id: null,
      status: null,
      task_date: formatDate(today),
      date_end: formatDate(today),
      filter_task_date: formatDateLocalTime(today),
      created_by: null,
      creating_type: null,
      reason_ids: null,
      task_status: null,
    },
  });

  // Watch all search fields
  const searchFields = watch([
    "delegate_id",
    "status",
    "task_date",
    "date_end",
    "created_by",
    "creating_type",
    "client_id",
    "reason_ids",
    "task_status",
  ]);

  // Validate task_date and date_end whenever they change
  useEffect(() => {
    const task_date = searchFields[2];
    const date_end = searchFields[3];

    if (task_date && date_end) {
      const fromDate = new Date(task_date);
      const toDate = new Date(date_end);

      if (fromDate > toDate) {
        setDateError(t("common.date_error"));
        toastr.error(
          t("common.date_error") || "Date from cannot be greater than date to"
        );
      } else {
        setDateError(null);
      }
    }
  }, [searchFields[2], searchFields[3], t]);

  // Parse URL params to set initial form values
  const parseUrlParams = () => {
    try {
      // Initial values object
      const initialValues = {
        delegate_id: null,
        status: null,
        task_date: formatDate(today),
        date_end: formatDate(today),
        created_by: null,
        creating_type: null,
        client_id: null,
        reason_ids: null,
        task_status: null,
      };

      // Initialize params object for API call
      const params = {};

      // Parse delegate IDs
      const delegateIds = queryParams.getAll("delegate_id[]");
      if (delegateIds.length > 0 && delegates?.result?.length > 0) {
        initialValues.delegate_id = delegateIds
          .map((id) =>
            delegates.result
              .map((item) => ({
                label: item.full_name,
                value: item.id,
              }))
              .find((option) => option.value.toString() === id.toString())
          )
          .filter(Boolean);

        // Add to params
        params["delegate_id[]"] = delegateIds;
      }

      // Parse status
      const statusIds = queryParams.getAll("status[]");
      if (statusIds.length > 0) {
        initialValues.status = statusIds
          .map((status) => ({
            label: t(`types.reason.${status}`),
            value: parseInt(status),
          }))
          .filter(Boolean);

        // Add to params
        params["status[]"] = statusIds;
      }

      // Parse date range
      const dateStart = queryParams.get("task_date");
      if (dateStart) {
        initialValues.task_date = dateStart;
        params["task_date[from]"] = dateStart;
      } else {
        // Set default to today if no date in URL
        params["task_date[from]"] = formatDate(today);
      }

      const dateEnd = queryParams.get("date_end");
      if (dateEnd) {
        initialValues.date_end = dateEnd;
        params["task_date[to]"] = dateEnd;
      } else {
        // Set default to today if no date in URL
        params["task_date[to]"] = formatDate(today);
      }

      // Parse creating type
      const creatingTypeIds = queryParams.getAll("creating_type[]");
      if (creatingTypeIds.length > 0) {
        initialValues.creating_type = creatingTypeIds
          .map((type) => ({
            label: t(
              type === "4"
                ? "clients.client"
                : type === "3"
                ? "common.delegate"
                : "common.admin"
            ),
            value: parseInt(type),
          }))
          .filter(Boolean);

        // Add to params
        params["creating_type[]"] = creatingTypeIds;
      }

      // Parse reason_id
      const reasonIds = queryParams.getAll("reason_ids[]");
      if (reasonIds.length > 0 && bilTypes?.result?.length > 0) {
        initialValues.reason_ids = reasonIds
          .map((id) =>
            bilTypes.result
              .filter((item) => !item.is_default)
              .map((item) => ({
                label: i18n.language === "en" ? item.title?.en : item.title?.ar,
                value: item.id,
              }))
              .find((option) => option.value.toString() === id.toString())
          )
          .filter(Boolean);

        // Add to params
        params["reason_ids[]"] = reasonIds;
      }

      // Parse task_status
      const taskStatusIds = queryParams.getAll("task_status[]");
      if (taskStatusIds.length > 0 && bilTypes?.result?.length > 0) {
        initialValues.task_status = taskStatusIds
          .map((id) =>
            tasStatusBilTypes.result
              .filter((item) => item.is_default)
              .map((item) => ({
                label: i18n.language === "en" ? item.title?.en : item.title?.ar,
                value: item.type,
              }))
              .find((option) => option.value.toString() === id.toString())
          )
          .filter(Boolean);

        // Add to params
        params["task_status[]"] = taskStatusIds;
      }

      return { initialValues, params };
    } catch (error) {
      console.error("Error parsing URL parameters:", error);
      return {
        initialValues: {
          delegate_id: null,
          status: null,
          task_date: formatDate(today),
          date_end: formatDate(today),
          created_by: null,
          creating_type: null,
          client_id: null,
          reason_ids: null,
          task_status: null,
        },
        params: {},
      };
    }
  };

  // Initialize form after options are loaded
  useEffect(() => {
    if (delegates?.result?.length > 0 && clients?.result?.length > 0) {
      const { initialValues, params } = parseUrlParams();
      reset(initialValues);

      // If no URL params exist, set default date filter for today
      if (Object.keys(params).length === 0) {
        const todayFormatted = formatDate(today);
        const defaultParams = {
          "task_date[from]": todayFormatted,
          "task_date[to]": todayFormatted,
        };
        setSearchParams(defaultParams);
      } else {
        // Set initial search params from URL
        setSearchParams(params);
      }
    }
  }, [delegates?.result?.length, clients?.result?.length, queryParams]);

  // Add manual search function
  const handleManualSearch = () => {
    // If date validation error exists, don't update search params
    if (dateError) {
      return;
    }

    const params = {};

    // Delegate filter
    if (searchFields[0]) {
      if (Array.isArray(searchFields[0])) {
        if (searchFields[0].length > 0) {
          searchFields[0].forEach((delegate) => {
            if (!params["delegate_id[]"]) {
              params["delegate_id[]"] = [delegate.value];
            } else {
              params["delegate_id[]"].push(delegate.value);
            }
          });
        }
      } else if (searchFields[0].value) {
        params["delegate_id[]"] = [searchFields[0].value];
      }
    }

    // Date filters - only add if they have values
    if (searchFields[2] && searchFields[2].trim() !== "") {
      params["task_date[from]"] = searchFields[2];
    }

    if (searchFields[3] && searchFields[3].trim() !== "") {
      params["task_date[to]"] = searchFields[3];
    }

    // Creating type filter
    if (searchFields[5]) {
      if (Array.isArray(searchFields[5])) {
        if (searchFields[5].length > 0) {
          searchFields[5].forEach((type) => {
            if (!params["creating_type[]"]) {
              params["creating_type[]"] = [type.value];
            } else {
              params["creating_type[]"].push(type.value);
            }
          });
        }
      } else if (searchFields[5].value) {
        params["creating_type[]"] = [searchFields[5].value];
      }
    }

    if (searchFields[6]) {
      if (Array.isArray(searchFields[6])) {
        if (searchFields[6].length > 0) {
          searchFields[6].forEach((type) => {
            if (!params["client_id[]"]) {
              params["client_id[]"] = [type.value];
            } else {
              params["client_id[]"].push(type.value);
            }
          });
        }
      } else if (searchFields[6].value) {
        params["client_id[]"] = [searchFields[6].value];
      }
    }

    // Reason ID filter (searchFields[7])
    if (searchFields[7]) {
      if (Array.isArray(searchFields[7])) {
        if (searchFields[7].length > 0) {
          searchFields[7].forEach((reason) => {
            if (!params["reason_ids[]"]) {
              params["reason_ids[]"] = [reason.value];
            } else {
              params["reason_ids[]"].push(reason.value);
            }
          });
        }
      } else if (searchFields[7].value) {
        params["reason_ids[]"] = [searchFields[7].value];
      }
    }

    // Task Status filter (searchFields[8])
    if (searchFields[8]) {
      if (Array.isArray(searchFields[8])) {
        if (searchFields[8].length > 0) {
          searchFields[8].forEach((status) => {
            if (!params["task_status[]"]) {
              params["task_status[]"] = [status.value];
            } else {
              params["task_status[]"].push(status.value);
            }
          });
        }
      } else if (searchFields[8].value) {
        params["task_status[]"] = [searchFields[8].value];
      }
    }

    // Update search params
    setSearchParams(params);
    // Reset to first page when filters change
    setPagination(1);
    refetch();
  };

  const handleReset = () => {
    // Reset form fields
    reset({
      delegate_id: null,
      status: null,
      task_date: "",
      date_end: "",
      created_by: null,
      creating_type: null,
      client_id: null,
      reason_ids: null,
      task_status: null,
    });

    // Clear date validation errors
    setDateError(null);

    // Clear search params
    setSearchParams({});
    // Clear URL params
    navigate(pathname, { replace: true });
    // Reset page to 1
    setPagination(1);

    // Refetch data with empty params
    refetch();
  };

  // Add status options with useMemo to handle translations
  // const statusOptions = useMemo(
  //   () => [
  //     { label: t("types.reason.processing"), value: 4 },
  //     { label: t("types.reason.done"), value: 5 },
  //     { label: t("types.reason.cancelled"), value: 7 },
  //     { label: t("types.reason.rejected"), value: 6 },
  //     { label: t("types.reason.transfer"), value: 8 },
  //     { label: t("types.reason.delaying"), value: 2 },
  //     { label: t("types.reason.delayed"), value: 3 },
  //     { label: t("types.reason.new"), value: 1 },
  //   ],
  //   [t, i18n.language]
  // );

  const { data: bilTypes } = reasonsTypesQueries.useGetAll({});
  const { data: tasStatusBilTypes } = reasonsTypesQueries.useGetAll({});

  const statusOptions =
    bilTypes?.result
      ?.filter((item) => !item.is_default)
      ?.map((item) => ({
        label: i18n.language === "en" ? item.title?.en : item.title?.ar,
        value: item.id,
        type: item.type,
      })) || [];

  const taskStatusOptions =
    tasStatusBilTypes?.result
      ?.filter((item) => item.is_default)
      .map((item) => ({
        label: i18n.language === "en" ? item.title?.en : item.title?.ar,
        value: item.id,
        type: item.type,
      })) || [];

  // Filter fields configuration for SearchCard
  const SearchData = [
    {
      id: 1,
      label: t("common.delegate"),
      type: "select",
      name: "delegate_id",
      options:
        delegates?.result?.map((item) => ({
          label: item.full_name,
          value: item.id,
        })) || [],
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 2,
      label: t("types.reason.reason"),
      type: "select",
      name: "reason_ids",
      options: statusOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 6,
      label: t("types.reason.task_reason"),
      type: "select",
      name: "task_status",
      options: taskStatusOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 6,
      label: t("tasks.creating_type"),
      type: "select",
      name: "creating_type",
      options: [
        { label: t("clients.client"), value: 4 },
        { label: t("common.delegate"), value: 3 },
        { label: t("common.admin"), value: 2 },
      ],
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 5,
      label: t("clients.clients"),
      type: "select",
      name: "client_id",
      options:
        clients?.result?.map((item) => ({
          label:
            (item.full_name ? item.full_name : "---") +
            "/" +
            (item.company_name ? item.company_name : "---"),
          value: item.id,
        })) || [],
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
  ];

  // Input fields configuration for SearchCard
  const inputsArray = [
    {
      id: 3,
      name: "task_date",
      type: "date",
      label: t("common.start_date"),
      cols: 2,
      error: dateError ? { message: dateError } : null,
    },
    {
      id: 4,
      name: "date_end",
      type: "date",
      label: t("common.end_date"),
      cols: 2,
      error: dateError ? { message: dateError } : null,
    },
  ];

  const { data: transfereList } = allTasksQueries.useGetAllTransfer();

  const [optionBondTypesGroup, setOptionBondTypesGroup] = useState([]); // Assuming you have a way to populate this
  const [optionsReasonsList, setOptionReasonsList] = useState([]); // Assuming you have a way to populate this

  useEffect(() => {
    if (delegates?.result?.length > 0) {
      setOptionBondTypesGroup(
        delegates.result.map((item) => ({
          label: item?.full_name,
          value: item.id,
        }))
      );
    }
  }, [delegates?.result]);

  useEffect(() => {
    if (transfereList?.data?.length > 0) {
      setOptionReasonsList(
        transfereList.data.map((item) => ({
          label: i18n.language === "eng" ? item?.title.en : item?.title.ar,
          value: item.id,
        }))
      );
    }
  }, [transfereList?.data]);

  const handelOpenTransferTask = () => {
    setOpenTransfereList(true);
  };

  const hadnelCloseTransfereTask = () => {
    setOpenTransfereList(false);
    reset();
    setSelectId(0);
    setSelectedDelegate(null);
  };

  const handelAccept = async (data) => {
    if (!data.reason_id.value && data.reason_id.value !== 0) {
      toastr.error(t("tasks.reason_required"));
      return;
    }
    if (!data.transfer_delegate_id?.value) {
      toastr.error(t("tasks.delegate_requierd"));
      return;
    }

    try {
      const response = await tasksAPis.transfer({
        payload: {
          delegate_id: data.transfer_delegate_id.value,
          reason_id: data.reason_id.value === 0 ? null : data.reason_id.value,
          new_transfer: data.reason_id.value
            ? null
            : { en: data.new_transfer, ar: data.new_transfer },
        },
        id: selectId,
      });
      hadnelCloseTransfereTask();
      refetch();
      toastr.success(response.message);
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const handelCloseDoneModel = () => {
    setOpenDone(false);
    reset();
  };

  const handelDoneTask = async () => {
    try {
      setIsDeleting(true);
      const response = await tasksAPis.done({
        payload: {
          "admin-note": watch("adminNote"),
        },
        id: selectId,
      });
      handelCloseDoneModel();
      refetch();
      reset();
      setIsDeleting(false);
      setSelectId(null);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
  };

  const hadnelApprove = async () => {
    try {
      if (
        !watch("task_date") &&
        selectedCreatingType === 4 &&
        watch("button_type") === 1
      ) {
        setError("task_date", {
          types: "manual",
          message: t("common.field_required"),
        });
        return;
      }
      setIsDeleting(true);
      const dataToSend = {
        delegate_id: watch("filter_delegate_id").value,
        created_by: selectedCreatingType,
        status: watch("button_type"),
        task_date: watch("filter_task_date")
          ? formatISOToCustomDate(watch("filter_task_date"))
          : null,
      };
      const response = await tasksAPis.approve({
        id: selectId,
        payload: dataToSend,
      });
      // setIsClient(false);
      setSelectedCteatingType(null);
      handelCLoseModal();
      reset();
      setValue("filter_task_date", formatDateLocalTime(today));
      refetch();
      setIsDeleting(false);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
  };

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    // setTransactionId(null);
    setOpenCancelModal(false);
    setOpenApproveModel(false);
    setSelectedCteatingType(null);
    setValue("delegate_id", null);
    setValue("button_type", 0);
    setOpenDone(false);
    clearErrors();
    setSelectedDelegate(null);
  };

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    setOpen(true);
  };
  const handelCloseSideBar = () => {
    setOpen(false);
  };
  const handelCanecel = async () => {
    try {
      setIsDeleting(true);
      await tasksAPis.cancel({
        id: selectId,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      setSelectId(null);
      toastr.success("Cancel is Done");
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("common.tasks"),
      link: pathname,
    },
  ];

  function getKeyByValue(value) {
    return Object.keys(AdmminAprovaple).find(
      (key) => AdmminAprovaple[key] === value
    );
  }

  function getKeyByValueCreatingType(value) {
    return Object.keys(CreatingType).find((key) => CreatingType[key] === value);
  }

  const toggleMenu = (id) => {
    setOpenMenu((prev) => (prev === id ? null : id)); // Open menu for current row, close if same row is clicked again
  };

  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header: "#",
        width: 50,
        accessor: (cellProps) => (
          <div>
            {cellProps.admin_approval_number === 3 ? (
              <p
                style={{
                  width: "20px",
                  height: "20px",
                  color: "#fff",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  borderRadius: "50%",
                  background: "#dd1313",
                }}
              >
                {cellProps.id_toShow}
              </p>
            ) : (
              <p>{cellProps.id_toShow}</p>
            )}
          </div>
        ),
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("tasks.task_type"),
        accessor: "task_type",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("clients.clients"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.delegate"),
        accessor: "delegate",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("tasks.creating_type"),
        accessor: "creating_type",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("tasks.task_date"),
        accessor: "task_date",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("tasks.task_time"),
        accessor: "task_time",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("tasks.deadline"),
        accessor: "duo_at",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.reason_types"),
        accessor: (cellProps) => (
          <div
            style={{
              background: cellProps?.task_status?.color,
              paddingBlock: 6,
              paddingInline: 5,
              borderRadius: 20,
              colorScheme: "light dark",
              textAlign: "center",
            }}
          >
            {cellProps?.task_status?.title}
          </div>
        ),
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("types.reason.reason"),
        accessor: "reason",
        disableFilters: true,
        filterable: false,
      },
    ];

    const permissions = [
      "task.update",
      "task.destroy",
      "task.accept_task",
      "task.transfer_task",
      "task.cancel_task",
      "task.done_task",
      "task.show",
    ];

    // Check if user has any of the required permissions
    const hasPer = permissions.some((permission) => hasPermission(permission));
    // Only add actions column if user has any permission
    if (hasPer) {
      baseColumns.push({
        Header: t("common.actions"),
        accessor: (cellProps) => {
          const isDefault = cellProps.isDefault === 1;

          return (
            <DropdownMenu.Root>
              <DropdownMenu.Trigger asChild>
                <button className="icon-button">
                  <HiDotsVertical size={18} />
                </button>
              </DropdownMenu.Trigger>

              <DropdownMenu.Portal>
                <DropdownMenu.Content
                  side="bottom"
                  align="start"
                  sideOffset={4}
                  className="dropdown-content"
                >
                  <Can permission="task.update">
                    {![
                      ReasonsEnum.CANCELLED,
                      ReasonsEnum.REJECTED,
                      ReasonsEnum.DONE,
                      ReasonsEnum.PROCESSING,
                      ReasonsEnum.TRANSFER,
                    ].includes(cellProps.statusCond) &&
                      !isDefault && (
                        <DropdownMenu.Item
                          className="dropdown-item text-primary"
                          onSelect={() => {
                            setSelectId(cellProps.id);
                            setTransactionId(cellProps.task_transaction_id);
                            setOpen(true);
                          }}
                        >
                          <FaPenToSquare size={14} className="mx-2" />
                          {t("common.update")}
                        </DropdownMenu.Item>
                      )}
                  </Can>

                  <Can permission="task.destroy">
                    {cellProps.statusCond !== ReasonsEnum.PROCESSING &&
                      cellProps.statusCond !== ReasonsEnum.TRANSFER &&
                      cellProps.creating_type_number !== 4 &&
                      !isDefault && (
                        <DropdownMenu.Item
                          className="dropdown-item text-danger"
                          onSelect={() => {
                            handelOpenModal();
                            handelSelectId(cellProps.id);
                          }}
                        >
                          <MdDeleteSweep size={18} className="mx-2" />
                          {t("common.delete")}
                        </DropdownMenu.Item>
                      )}
                  </Can>

                  <Can permission="task.accept_task">
                    {cellProps.admin_approval_number === 3 && !isDefault && (
                      <DropdownMenu.Item
                        className="dropdown-item text-warning"
                        onSelect={() => {
                          setOpenApproveModel(true);
                          handelSelectId(cellProps.task_transaction_id);
                          setSelectedCteatingType(
                            cellProps.creating_type_number
                          );
                          setValue("filter_delegate_id", {
                            label: cellProps?.other_delegate?.full_name,
                            value: cellProps?.other_delegate?.id,
                          });
                          setSelectedDelegate(cellProps.delegate_id);
                        }}
                      >
                        <GrStatusGood size={14} className="mx-2" />
                        {t("types.reason.admin_approve")}
                      </DropdownMenu.Item>
                    )}
                  </Can>

                  <Can permission="task.transfer_task">
                    {![
                      ReasonsEnum.TRANSFER,
                      ReasonsEnum.CANCELLED,
                      ReasonsEnum.REJECTED,
                      ReasonsEnum.DONE,
                      ReasonsEnum.PROCESSING,
                    ].includes(cellProps.statusCond) &&
                      cellProps.creating_type_number !== 4 && (
                        <DropdownMenu.Item
                          className="dropdown-item text-primary"
                          onSelect={() => {
                            handelOpenTransferTask(true);
                            setSelectId(cellProps.task_transaction_id);
                          }}
                        >
                          <BiTransfer size={14} className="mx-2" />
                          {t("common.transfer")}
                        </DropdownMenu.Item>
                      )}
                  </Can>

                  <Can permission="task.cancel_task">
                    {![
                      ReasonsEnum.CANCELLED,
                      ReasonsEnum.REJECTED,
                      ReasonsEnum.DONE,
                      ReasonsEnum.PROCESSING,
                    ].includes(cellProps.statusCond) && (
                      <DropdownMenu.Item
                        className="dropdown-item text-warning"
                        onSelect={() => {
                          setOpenCancelModal(true);
                          setSelectId(cellProps.task_transaction_id);
                        }}
                      >
                        <MdCancel size={14} className="mx-2" />
                        {t("common.cancel")}
                      </DropdownMenu.Item>
                    )}
                  </Can>

                  <Can permission="task.done_task">
                    {![
                      ReasonsEnum.TRANSFER,
                      ReasonsEnum.CANCELLED,
                      ReasonsEnum.REJECTED,
                      ReasonsEnum.DONE,
                    ].includes(cellProps.statusCond) && (
                      <DropdownMenu.Item
                        className="dropdown-item text-secondary"
                        onSelect={() => {
                          setOpenDone(true);
                          setSelectId(cellProps.task_transaction_id);
                        }}
                      >
                        <IoCheckmarkDoneCircle size={14} className="mx-2" />
                        {t("tasks.done-task")}
                      </DropdownMenu.Item>
                    )}
                  </Can>

                  <Can permission="task.show">
                    <DropdownMenu.Item
                      className="dropdown-item text-success"
                      onSelect={() => {
                        setSelectId(cellProps.id);
                        setIsShow(true);
                        setOpen(true);
                      }}
                    >
                      <FaInfoCircle size={14} className="mx-2" />
                      {t("common.show")}
                    </DropdownMenu.Item>
                  </Can>
                </DropdownMenu.Content>
              </DropdownMenu.Portal>
            </DropdownMenu.Root>
          );
        },
        disableFilters: true,
        filterable: false,
      });
    }

    return baseColumns;
  }, [openMenu, t, i18n.language]);

  const rowData = useMemo(
    () =>
      data?.data?.length > 0
        ? data?.data
            .map((item, index) => ({
              task_transaction_id: item.id,
              id: item.task_id,
              id_toShow: (pagination - 1) * 10 + index + 1,
              delegate: item.delegate.full_name || "----",
              client:
                (item.client.full_name ? item.client.full_name : "---") +
                  "/" +
                  (item.client.company_name
                    ? item.client.company_name
                    : "---") || "----",
              reason: item.reason?.title || "----",
              status: t(
                `types.reason.${getReasonsKeyByValue(
                  item.status
                ).toLowerCase()}`
              ),
              statusCond: item.status,
              admin_approval: getKeyByValue(item.admin_approval),
              admin_approval_number: item.admin_approval,
              creating_type: t(
                item.creating_type === 4
                  ? "clients.client"
                  : item.creating_type === 3
                  ? "common.delegate"
                  : "common.admin"
              ),
              creating_type_number: item.creating_type,
              task_date: item.task_date?.split(" ")[0] || "---",
              task_type: item.task_type?.title,
              task_time: item.task_date?.split(" ")[1] || "---",
              duo_at: item.duo_at?.split(" ")[0] || "---",
              other_delegate: item?.other_delegate,
              task_status: item?.task_status,
              delegate_id: item?.delegate?.id,
            }))
            .reverse()
        : [],
    [data?.data, i18n.language, t, pagination]
  );

  const deleteFUn = async () => {
    try {
      setIsDeleting(true);
      const response = await tasksAPis.deleteFun({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("common.tasks")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("common.tasks")}
          canPermission="task.store"
          isAddOptions={true}
          handleOrderClicks={handelAddBonds}
        />
        <Card style={{ height: "80vh", padding: 20 }}>
          <TableContainer
            customComponent={
              <SearchCard
                SearchData={SearchData}
                control={control}
                hadelReset={handleReset}
                inputsArray={inputsArray}
                register={register}
                watch={watch}
                setValue={setValue}
                handelSearch={handleManualSearch}
                hasButtonSearch={true}
              />
            }
            hideSHowGFilter={false}
            columns={columns || []}
            data={rowData || []}
            setPage={setPagination}
            // customHeight={"53vh"}
            pageCount={data?.meta?.last_page}
            currentPage={pagination}
            isLoading={isLoading}
          />
        </Card>
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={deleteFUn}
          itemName={t("common.tasks")}
          isDeleting={isDeleting}
        />
      </Container>
      {openCancelModal && selectId && (
        <Modal isOpen={openCancelModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.no")}
              </Button>
              <Button
                onClick={() => handelCanecel()}
                disabled={isDeleting}
                type="button"
                color="danger"
                className="btn-sm"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}

      {openTransferList && (
        <Modal isOpen={openTransferList} backdrop="static">
          <ModalHeader toggle={hadnelCloseTransfereTask}>
            {t("tasks.transfer_tasks")}
          </ModalHeader>
          <ModalBody>
            <form onSubmit={handleSubmit(handelAccept)}>
              <Row className="g-2">
                <Col xs={12} className="mb-2">
                  <CustomSelect
                    control={control}
                    error={errors.delegate_id}
                    label={t("common.delegate")}
                    options={optionBondTypesGroup.filter(
                      (item) =>
                        item.value !==
                        data?.data?.find((item) => item.id === Number(selectId))
                          .delegate?.id
                    )}
                    name="transfer_delegate_id"
                  />
                </Col>
                <Col xs={12} className="mb-2">
                  <CustomSelect
                    control={control}
                    error={errors.reason_id}
                    label={t("types.reason.reasons")}
                    options={optionsReasonsList}
                    name="reason_id"
                  />
                </Col>
              </Row>
              <ModalFooter>
                <Button
                  type="button"
                  color="light"
                  className="btn-sm"
                  onClick={hadnelCloseTransfereTask}
                >
                  {t("common.close")}
                </Button>
                <Button
                  disabled={isSubmitting || !watch("reason_id")?.value}
                  type="submit"
                  className="btn-sm"
                  color="primary"
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.transfer")
                  )}
                </Button>
              </ModalFooter>
            </form>
          </ModalBody>
        </Modal>
      )}

      <Modal isOpen={openDone} backdrop="static">
        <ModalHeader toggle={handelCLoseModal}>
          {t("tasks.done-task")}
        </ModalHeader>
        <ModalBody>
          <form>
            <Col xs={12} className="mb-2">
              <TextAreaField
                name="adminNote"
                control={control}
                placeholder={t("common.note")}
                defaultValue=""
                className="mb-2"
                rows={4}
                error={errors?.adminNote}
              />
            </Col>
            <ModalFooter>
              <Button
                type="button"
                color="light"
                disabled={isDeleting}
                className="btn-sm"
                onClick={() => {
                  handelCloseDoneModel();
                }}
              >
                {t("common.cancel")}
              </Button>
              <Button
                onClick={handelDoneTask}
                disabled={isDeleting}
                color="primary"
                className="btn-sm"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.accept")
                )}
              </Button>
            </ModalFooter>
          </form>
        </ModalBody>
      </Modal>

      <Modal isOpen={openApproveModel} backdrop="static">
        <ModalHeader toggle={handelCLoseModal}>
          {t("types.reason.admin_approve")}
        </ModalHeader>
        <ModalBody>
          <form onSubmit={handleSubmit(handelAccept)}>
            <Row className="g-1">
              <Col xs={selectedCreatingType === 4 ? 6 : 12} className="mb-2">
                <CustomSelect
                  control={control}
                  error={errors.delegate_id}
                  label={t("common.delegate")}
                  options={optionBondTypesGroup
                    ?.filter((item) => item.id !== selectId)
                    .filter((item) => item?.value !== selectedDelegate)}
                  name="filter_delegate_id"
                  isDisabled={isShow}
                />
              </Col>
              {selectedCreatingType === 4 && (
                <Col xs={6}>
                  <CustomInput
                    control={control}
                    error={errors.filter_task_date}
                    isDisabled={isShow}
                    placeholder={t("tasks.tasks_date")}
                    type="datetime-local"
                    name="filter_task_date"
                    min={new Date().toISOString().slice(0, 16)}
                  />
                </Col>
              )}
            </Row>
            <ModalFooter>
              <Button
                type="button"
                color="danger"
                disabled={isDeleting}
                className="btn-sm"
                onClick={() => {
                  setValue("button_type", 2);
                  hadnelApprove();
                }}
              >
                {watch("button_type") === 2 && isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.reject")
                )}
              </Button>
              <Button
                disabled={isDeleting || !watch("filter_delegate_id")}
                onClick={() => {
                  setValue("button_type", 1);
                  hadnelApprove();
                }}
                color="primary"
                className="btn-sm"
              >
                {watch("button_type") === 1 && isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.accept")
                )}
              </Button>
            </ModalFooter>
          </form>
        </ModalBody>
      </Modal>
      <TypesModel
        open={open}
        handelClose={() => {
          handelCloseSideBar();
          setSelectId(null);
          setIsShow(false);
        }}
        hideAll={true}
        content={
          <div>
            <ActionTask
              handelClose={() => {
                handelCloseSideBar();
                setSelectId(null);
                setIsShow(false);
              }}
              isShow={isShow}
              selectId={selectId}
              taskTransactionId={transactionId}
              refetch={refetch}
            />
          </div>
        }
      />
    </div>
  );
};
export default Tasks;
