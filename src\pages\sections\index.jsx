import { Button, Container } from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { handleBackendErrors, hasPermission } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import ActionSections from "./action-sections";
import { sectionsQueries } from "../../apis/sections/query";
import TypesModel from "../../components/Common/types-model";
import DeleteModal from "../../components/Common/DeleteModal";
import { sectionsAPis } from "../../apis/sections/api";
import Terms from "../terms";
import { contractTemplateQueries } from "../../apis/contract_templete/query";

const Sections = ({ templateId, setSelectedPage }) => {
  const [pagination, setPagination] = useState(1);
  const { t, i18n } = useTranslation();
  const { data, isLoading, refetch } = sectionsQueries.useGetAll({
    limit: 10,
    page: pagination,
    sectionId: templateId,
  });

  const { data: template } = contractTemplateQueries.useGet({ id: templateId });

  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openMenu, setOpenMenu] = useState(false);
  const [open, setOpen] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [show, setShow] = useState(false);

  // Add effect to watch language changes
  useEffect(() => {
    refetch();
  }, [i18n.language]);

  const handelCLoseModal = () => {
    setOpen(false);
    setIsShow(false);
    setSelectId(0);
    setOpenDeleteModal(false);
    refetch();
  };

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("common.sections"),
      //   link: pathname,
    },
  ];

  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.title"),
        accessor: "title",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.order"),
        accessor: "order",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.is_active"),
        accessor: "is_active",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.is_editable"),
        accessor: "is_editable",
        disableFilters: true,
        filterable: false,
      },
    ];

    const permissions = [
      "task.update",
      "task.destroy",
      "task.accept_task",
      "task.transfer_task",
      "task.cancel_task",
      "task.done_task",
      "task.show",
    ];

    // Check if user has any of the required permissions
    const hasPer = permissions.some((permission) => hasPermission(permission));
    // Only add actions column if user has any permission
    if (hasPer) {
      baseColumns.push({
        Header: t("common.actions"),
        accessor: (cellProps) => {
          return (
            <ul
              className="d-flex align-items-center gap-2 justify-content-start"
              style={{ cursor: "pointer" }}
            >
              <Can permission={"task.update"}>
                <div
                  onClick={() => {
                    setSelectId(cellProps.id);
                    setOpen(true);
                  }}
                >
                  <i className=" ri-pencil-fill text-success cursor-pointer font-size-16"></i>
                  {/* {t("common.update")} */}
                </div>
              </Can>
              <Can permission={"task.destroy"}>
                <div
                  onClick={() => {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }}
                >
                  <i className=" ri-delete-bin-fill align-middle me-2 text-danger"></i>
                  {/* {t("common.delete")} */}
                </div>
              </Can>

              <Can permission={"task.show"}>
                <div
                  onClick={() => {
                    setSelectId(cellProps.id);
                    setIsShow(true);
                    setOpen(true);
                  }}
                >
                  <i className="ri-slideshow-2-fill align-middle me-2"></i>
                  {/* {t("common.show")} */}
                </div>
              </Can>
              {/* </DropdownMenu> */}
              {/* </Dropdown> */}
            </ul>
          );
        },
        disableFilters: true,
        filterable: false,
      });
    }

    return baseColumns;
  }, [openMenu, t, i18n.language]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data?.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (pagination - 1) * 10 + index + 1,
              title: item.title,
              order: item.order,
              is_editable:
                item.is_editable === 1
                  ? t("common.active")
                  : t("common.in_active"),
              is_active:
                item.is_active === 1
                  ? t("common.active")
                  : t("common.in_active"),
            }))
            .sort((a, b) => b.order - a.order)
        : [],
    [data?.result, i18n.language, t, pagination]
  );

  const deleteFUn = async () => {
    try {
      setIsDeleting(true);
      const response = await sectionsAPis.deleteFu({
        id: selectId,
      });
      refetch();
      toastr.success(response?.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
  };

  return (
    <div>
      <Container fluid>
        <Breadcrumbs
          title={
            t("common.sections") +
            " " +
            t("common.for") +
            " " +
            template?.result?.name
          }
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("common.section")}
          canPermission="task.store"
          isAddOptions={true}
          handleOrderClicks={() => setOpen(true)}
        />
        <TableContainer
          hideSHowGFilter={false}
          columns={columns || []}
          data={rowData || []}
          setPage={setPagination}
          pageCount={data?.meta?.last_page}
          currentPage={pagination}
          isLoading={isLoading}
          customPageSize={true}
        />
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={deleteFUn}
          itemName={t("common.section")}
          isDeleting={isDeleting}
        />
      </Container>
      <TypesModel
        open={open}
        handelClose={() => {
          setSelectId(null);
          setOpen(false);
          setShow(false);
        }}
        customModelStyle={{
          maxWidth: selectId ? "60vw" : "30vw",
          maxHeigh: "80vh",
          overflow: "hidden",
        }}
        hideAll={true}
        content={
          <div>
            <ActionSections
              handelClose={() => {
                setSelectId(null);
                setOpen(false);
                setIsShow(false);
                refetch();
                setShow(false);
              }}
              setShow={setShow}
              isShow={isShow}
              selectId={selectId}
              refetch={refetch}
              templateId={templateId}
              cusomComponent={
                show && (
                  <div>
                    {selectId && <Terms sectionId={selectId} isShow={isShow} />}
                  </div>
                )
              }
            />
          </div>
        }
      />

      <Button
        color="primary"
        className="btn-sm"
        onClick={() => setSelectedPage("template")}
      >
        {t("common.back_to_template")}
      </Button>
    </div>
  );
};
export default Sections;
