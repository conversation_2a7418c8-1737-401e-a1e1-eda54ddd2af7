import { Container } from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import toastr from "toastr";
import { handleBackendErrors, hasPermission } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import { sectionsQueries } from "../../apis/terms/query";
import DeleteModal from "../../components/Common/DeleteModal";
import { TermsAPis } from "../../apis/terms/api";
import TermsActions from "./action-sections";
import { MdDeleteSweep } from "react-icons/md";
import { FaPenToSquare } from "react-icons/fa6";

const Terms = ({ isShow, sectionId }) => {
  const [pagination, setPagination] = useState(1);
  const { t, i18n } = useTranslation();
  const { data, isLoading, refetch, isRefetching } = sectionsQueries.useGetAll({
    limit: 10,
    page: pagination,
    sectionId,
  });

  const [selectId, setSelectId] = useState(null);
  const [selectIdForDelete, setSelectIdForDelete] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openMenu, setOpenMenu] = useState(false);
  const [open, setOpen] = useState(false);
  // const [isShow, setIsShow] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Add effect to watch language changes
  useEffect(() => {
    refetch();
  }, [i18n.language]);

  const handelCLoseModal = () => {
    setOpen(false);
    // setIsShow(false);
    setSelectId(0);
    setOpenDeleteModal(false);
    refetch();
  };

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.title"),
        accessor: "title",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.order"),
        accessor: "order",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.is_active"),
        accessor: "is_active",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.is_editable"),
        accessor: "is_editable",
        disableFilters: true,
        filterable: false,
      },
    ];

    const permissions = [
      "task.update",
      "task.destroy",
      "task.accept_task",
      "task.transfer_task",
      "task.cancel_task",
      "task.done_task",
      "task.show",
    ];

    // Check if user has any of the required permissions
    const hasPer = permissions.some((permission) => hasPermission(permission));
    // Only add actions column if user has any permission
    if (hasPer && !isShow) {
      baseColumns.push({
        Header: t("common.actions"),
        accessor: (cellProps) => {
          return (
            <ul className="d-flex align-items-center gap-2 justify-content-start">
              <Can permission={"task.update"}>
                <div
                  onClick={() => {
                    setSelectId(cellProps.id);
                    setOpen(true);
                  }}
                >
                  {/* <i className=" ri-pencil-fill text-success cursor-pointer font-size-16"></i> */}
                  <FaPenToSquare size={14} />

                  {/* {t("common.update")} */}
                </div>
              </Can>
              <Can permission={"task.destroy"}>
                <div
                  onClick={() => {
                    handelOpenModal();
                    // handelSelectId(cellProps.id);
                    setSelectIdForDelete(cellProps.id);
                  }}
                >
                  {/* <i className=" ri-delete-bin-fill text-danger cursor-pointer font-size-16"></i> */}
                  <MdDeleteSweep size={18} />

                  {/* {t("common.delete")} */}
                </div>
              </Can>
            </ul>
          );
        },
        disableFilters: true,
        filterable: false,
      });
    }

    return baseColumns;
  }, [openMenu, t, i18n.language]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data?.result
            .filter((item) => item.id !== selectId)
            .map((item, index) => ({
              id: item.id,
              id_toShow: (pagination - 1) * 10 + index + 1,
              title: item.text,
              order: item.order,
              is_editable:
                item.is_editable === 1
                  ? t("common.active")
                  : t("common.in_active"),
              is_active:
                item.is_active === 1
                  ? t("common.active")
                  : t("common.in_active"),
            }))
            .reverse()
        : [],
    [data?.result, i18n.language, t, pagination, selectId]
  );

  const deleteFUn = async () => {
    try {
      setIsDeleting(true);
      const response = await TermsAPis.deleteFu({
        id: selectIdForDelete,
      });
      refetch();
      toastr.success(response?.message);
      setIsDeleting(false);
      handelCLoseModal();
      setSelectIdForDelete(0);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
  };

  return (
    <div>
      <Container fluid>
        <TermsActions
          handelClose={() => {
            setSelectId(null);
            refetch();
          }}
          selectId={selectId}
          refetch={refetch}
          sectionId={sectionId}
          isShow={isShow}
        />
        <TableContainer
          hideSHowGFilter={false}
          columns={columns || []}
          data={rowData || []}
          setPage={setPagination}
          pageCount={data?.meta?.last_page}
          currentPage={pagination}
          isLoading={isLoading || isRefetching}
          isSmall
        />
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={deleteFUn}
          itemName={t("common.trem")}
          isDeleting={isDeleting}
        />
      </Container>
    </div>
  );
};
export default Terms;
