import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Col, Container, Label, Row } from "reactstrap";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { ProductTypesAPis } from "../../apis/types/product-types/api";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { handleBackendErrors } from "../../helpers/api_helper";
import { ProductTypesQueries } from "../../apis/types/product-types/query";
import { billTypesQueries } from "../../apis/types/bill-type/query";
import { ContractQueries } from "../../apis/types/contract-type/query";
import { useTranslation } from "react-i18next";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import CustomTextArea from "../../components/Common/textArea";

const ProductsUnitsType = ({ isShow, selectId, handelClose }) => {
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [optionGroup2, setOptionGroup2] = useState([]); // Assuming you have a way to populate this

  const { data, isLoading } = ProductTypesQueries.useGet({
    id: Number(selectId),
  });
  const { t, i18n } = useTranslation();
  const { data: Contract } = ContractQueries.useGetAllContractTypes({});
  const { data: Bill } = billTypesQueries.useGetAll({});

  const handelCancel = () => {
    reset();
    handelClose();
  };

  const schema = yup
    .object({
      contract_types: yup
        .array()
        .of(
          yup.object().shape({
            label: yup.string().required(t("common.field_required")),
            value: yup.number().required(t("common.field_required")),
          })
        )
        .min(1, t("common.field_required")),
      bill_types: yup
        .array()
        .of(
          yup.object().shape({
            label: yup.string().required(t("common.field_required")),
            value: yup.number().required(t("common.field_required")),
          })
        )
        .min(1, t("common.field_required")),
    })
    .required();

  const statusOptions = [
    { value: 1, label: t("common.active") },
    { value: 2, label: t("common.in_active") },
  ];

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    watch,
    control,
    setError,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
      contract_types: null,
      bill_types: null,
      status: {
        value: 1,
        label: t("common.active"),
      },
    },
    // resolver: yupResolver(schema),
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Map the clients array to match the format required by react-select
      const clientsToReset = data?.result?.contract_types.map((item) => ({
        label: i18n.language === "eng" ? item.title.en : item.title.ar,
        value: item.id,
      }));

      // Set the options for the select dropdown
      // setOptionGroup2(clientsToReset);

      const options2 = data?.result?.bill_types.map((item) => ({
        label: i18n.language === "eng" ? item.title.en : item.title.ar,
        value: item.id,
      }));
      // setOptionGroup(options2);
      // Populate form with role data when loaded
      reset({
        titleArabic: data?.result?.title.ar || "----",
        titleEnglish: data?.result?.title.en || "--",
        descriptionArabic: data?.result?.description.ar || "---",
        descriptionEnglish: data?.result?.description.en || "---",
        // status: data?.result?.status,
        bill_types: options2,
        contract_types: clientsToReset,
        status: {
          value: data?.result.status,
          label: statusOptions.find(
            (item) => item.value === data?.result?.status
          )?.label,
        },
      });
    }
  }, [selectId, isLoading, data, reset]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      const response = await ProductTypesAPis.update({
        dataToSend: {
          title: {
            en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
            ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
          },
          description: {
            en: data.descriptionEnglish,
            ar: data.descriptionArabic,
          },
          status: Number(data.status.value),
          bill_types: data.bill_types.map((item) => Number(item.value)),
          contract_types: data.contract_types.map((item) => Number(item.value)),
        },
        id: Number(selectId),
      });
      toastr.success(response.message);
      reset();
      handelClose();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    try {
      const reposne = await ProductTypesAPis.add({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        description: {
          en: data.descriptionEnglish,
          ar: data.descriptionArabic,
        },
        status: Number(data.status.value),
        bill_types: data.bill_types.map((item) => Number(item.value)),
        contract_types: data.contract_types.map((item) => Number(item.value)),
      });
      toastr.success(reposne.message);
      reset(); // Reset form after successful submission
      handelClose();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.error("Error:", error);
    }
  };

  useEffect(() => {
    if (Bill?.result?.length > 0) {
      setOptionGroup([]);
      setOptionGroup((prev) => [
        ...prev,
        ...Bill?.result?.map((item) => ({
          label: i18n.language === "eng" ? item.title.en : item.title.ar,
          value: item.id,
        })),
      ]);
    }
  }, [Bill?.result?.length, i18n.language]);

  useEffect(() => {
    if (Contract?.result?.length > 0) {
      setOptionGroup2([]);
      setOptionGroup2((prev) => [
        ...prev,
        ...Contract?.result?.map((item) => ({
          label: i18n.language === "eng" ? item.title.en : item.title.ar,
          value: item.id,
        })),
      ]);
    }
  }, [Contract?.result?.length, i18n.language]);

  return (
    <div>
      <Container fluid>
        <Row>
          <form
            onSubmit={selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)}
          >
            {isLoading ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <Row
                style={{
                  height: "70vh",
                  overflowY: "auto",
                  overflowX: "hidden",
                  paddingBlock: 10,
                }}
              >
                <Col xs={6}>
                  <div className="mb-2">
                    <CustomInput
                      name="titleEnglish"
                      control={control}
                      label={t("common.title_in_english")}
                      type="text"
                      placeholder={t("common.title_in_english")}
                      disabled={isShow}
                      error={errors.titleEnglish}
                      rules={{ required: t("common.field_required") }}
                    />
                  </div>
                </Col>
                <Col xs={6}>
                  <div className="mb-2">
                    <CustomInput
                      name="titleArabic"
                      control={control}
                      label={t("common.title_in_arabic")}
                      type="text"
                      placeholder={t("common.title_in_arabic")}
                      disabled={isShow}
                      error={errors.titleArabic}
                    />
                  </div>
                </Col>
                <div className="mb-2">
                  <Label className="form-label">
                    {t("common.description_in_arabic")}
                  </Label>
                  <CustomTextArea
                    name="descriptionArabic"
                    control={control}
                    placeholder={t("common.description_in_arabic")}
                    isShow={isShow}
                    rows={4}
                  />
                </div>
                <div className="mb-4">
                  <Label className="form-label">
                    {t("common.description_in_english")}
                  </Label>
                  <CustomTextArea
                    name="description-in-english"
                    control={control}
                    placeholder={t("common.description_in_english")}
                    isShow={isShow}
                    rows={4}
                  />
                </div>
                <Col xs={6}>
                  <div className="mb-4">
                    <CustomSelect
                      name="contract_types"
                      control={control}
                      label={t("types.product_types.select_contract_types")}
                      options={optionGroup2}
                      isMulti={true}
                      isDisabled={isShow}
                      error={errors.contract_types}
                      defaultValue={[]}
                    />
                  </div>
                </Col>
                <Col xs={6}>
                  <div className="mb-4">
                    <CustomSelect
                      name="bill_types"
                      control={control}
                      label={t("types.product_types.select_bill_types")}
                      options={optionGroup}
                      isMulti={true}
                      isDisabled={isShow}
                      error={errors.bill_types}
                      defaultValue={[]}
                    />
                  </div>
                </Col>
                <Col xs={12}>
                  <div className="mb-4">
                    <CustomSelect
                      name="status"
                      control={control}
                      label={t("common.status")}
                      options={statusOptions}
                      isMulti={false}
                      isDisabled={isShow}
                      error={errors.status}
                      menuHeight={80}
                      menuPosition="fixed"
                      rules={{ required: t("common.field_required") }}
                    />
                  </div>
                </Col>
              </Row>
            )}
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: 8,
                justifyContent: "end",
              }}
            >
              <Button
                type="button"
                color="light"
                onClick={handelCancel}
                className="btn-sm "
                style={{ height: "32px", width: "54px" }}
              >
                {t("common.close")}
              </Button>
              {!isShow && (
                <Button
                  color="primary"
                  className="btn-sm waves-effect waves-light primary-button"
                  type="submit"
                  disabled={
                    isSubmitting ||
                    (!watch("titleArabic") && !watch("titleEnglish"))
                  }
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectId ? (
                    t("common.update")
                  ) : (
                    t("common.add")
                  )}
                </Button>
              )}
            </div>
          </form>
        </Row>
      </Container>
    </div>
  );
};
export default ProductsUnitsType;
