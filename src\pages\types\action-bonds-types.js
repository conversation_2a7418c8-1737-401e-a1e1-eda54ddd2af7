import { <PERSON><PERSON>, Col, Container, Label, Row } from "reactstrap";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { BondsQueries } from "../../apis/bound/query";
import { BondsAPis } from "../../apis/bound/api";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { handleBackendErrors } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import CustomTextArea from "../../components/Common/textArea";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

const ActionBondsType = ({ isShow, selectedId, handelClose }) => {
  const {
    data: contractType,
    isLoading: isLoadingContractType,
    isRefetching,
  } = BondsQueries.useGetBonds({
    id: Number(selectedId),
  });
  const { t, i18n } = useTranslation();

  const schema = yup
    .object({
      payment_type: yup
        .object()
        .shape({
          label: yup.string().required(t("common.field_required")),
          value: yup.number().required(t("common.field_required")),
        })
        .required(t("common.field_required")),
    })
    .required();

  const { data: BondsSettings, isLoading } = BondsQueries.useGetBondsSettings();

  const [bondsSettingsOPtions, setBondsSettingsOPtions] = useState([]);

  useEffect(() => {
    if (BondsSettings?.result?.length > 0) {
      const options = BondsSettings.result.map((item) => ({
        label: i18n.language === "eng" ? item.title.en : item.title.ar,
        value: item.payment_type,
      }));
      setBondsSettingsOPtions(options);
    }
  }, [BondsSettings?.result, i18n.language]);

  const handelCancel = () => {
    reset();
    handelClose();
  };

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    watch,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
      bond: false,
      sign: false,
      total: false,
      type: 0,
      notes: "",
      payment_type: null,
      image: null, // Add image default value
    },
    resolver: yupResolver(schema),
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectedId > 0 && !isLoadingContractType && contractType?.result) {
      const options = bondsSettingsOPtions.find(
        (item) => contractType?.result?.payment_type === item.value
      );

      // Populate form with role data when loaded
      reset({
        titleArabic: contractType?.result?.title.ar || "---",
        titleEnglish: contractType?.result?.title.en || "----",
        descriptionArabic: contractType?.result?.description?.ar || "",
        descriptionEnglish: contractType?.result?.description?.en || "",
        total: contractType?.result?.total || false,
        bond: contractType?.result?.bond || false,
        sign: contractType?.result?.sign || false,
        payment_type: options || false,
        is_default: contractType?.result?.is_default || false,
        is_setting: contractType?.result?.is_setting || false,
        image: null, // File inputs can't be preset
      });
    }
  }, [
    selectedId,
    isLoadingContractType,
    contractType?.result,
    bondsSettingsOPtions,
  ]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const toBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result.split(",")[1]);
      reader.onerror = (error) => reject(error);
    });
  };

  const UpdateFun = async (data) => {
    let jsonPayload = {
      total: data.total ? 1 : 0,
      sign: data.sign ? 1 : 0,
      bond: data.bond ? 1 : 0,
      title: {
        en: data.titleEnglish,
        ar: data.titleArabic,
      },
      description: {
        en: data.descriptionEnglish,
        ar: data.descriptionArabic,
      },
      type: 1,
      payment_type: data.payment_type.value,
      is_setting: watch("is_setting"),
      is_default: watch("is_default"),
    };

    // Convert the file to Base64 and add it to the payload
    if (data.image && data.image[0]) {
      jsonPayload.image = await toBase64(new Blob(data.image));
    }

    try {
      const respomse = await BondsAPis.updateBonds({
        formData: jsonPayload,
        id: selectedId,
      });
      handelClose();
      toastr.success(respomse.message);
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    const formData = new FormData();

    // Append text fields
    // formData.append("notes", data.notes);
    // formData.append("type", data.type); // Assuming type is a number
    formData.append("total", data.total ? 1 : 0); // Assuming total is boolean (true/false)
    formData.append("sign", data.sign ? 1 : 0); // Assuming sign is boolean (true/false)
    formData.append("bond", data.bond ? 1 : 0); // Assuming bond is boolean (true/false)

    // Handle nested objects (title and description)
    formData.append(
      "title[en]",
      data.titleEnglish ? data.titleEnglish : data.titleArabic
    ); // Flatten title
    formData.append(
      "title[ar]",
      data.titleArabic ? data.titleArabic : data.titleEnglish
    );
    formData.append("description[en]", data.descriptionEnglish); // Flatten description
    formData.append("description[ar]", data.descriptionArabic);
    formData.append("type", 1);
    formData.append("payment_type", data.payment_type?.value);
    formData.append("is_setting", 0);
    formData.append("is_default", 0);

    // Append image or file if applicable
    if (data.file) {
      formData.append("file", data.file[0]); // Assuming you have a file input and it's in `data.file`
    }

    try {
      const response = await BondsAPis.addBonds(formData);
      toastr.success(response.message);
      reset(); // Reset form after successful submission
      handelClose();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.error("Error:", error);
    }
  };

  return (
    <div className="mt-8">
      <Container fluid>
        <Row>
          <form onSubmit={handleSubmit(selectedId ? UpdateFun : addFun)}>
            {isLoadingContractType || isLoading || isRefetching ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <>
                <Row>
                  <Col xs={6}>
                    <div className="mb-2">
                      <CustomInput
                        name="titleEnglish"
                        control={control}
                        label={t("common.title_in_english")}
                        type="text"
                        disabled={isShow}
                        error={errors.titleEnglish}
                        rules={{ required: true }}
                      />
                    </div>
                  </Col>
                  <Col xs={6}>
                    <div className="mb-2">
                      <CustomInput
                        name="titleArabic"
                        control={control}
                        label={t("common.title_in_arabic")}
                        type="text"
                        disabled={isShow}
                        error={errors.titleArabic}
                        rules={{ required: true }}
                      />
                    </div>
                  </Col>
                </Row>
                <div className="mb-4">
                  <Label className="form-label">
                    {t("common.description_in_english")}
                  </Label>
                  <CustomTextArea
                    name="descriptionEnglish"
                    control={control}
                    placeholder={t("common.description_in_english")}
                    isShow={isShow}
                    error={errors.descriptionEnglish}
                    rows={4}
                  />
                </div>
                <div className="mb-4">
                  <Label className="form-label">
                    {t("common.description_in_arabic")}
                  </Label>
                  <CustomTextArea
                    name="descriptionArabic"
                    control={control}
                    placeholder={t("common.description_in_arabic")}
                    isShow={isShow}
                    error={errors.descriptionArabic}
                    rows={4}
                  />
                </div>
                <Row>
                  <Col xs={12}>
                    <div className="mb-4">
                      <CustomSelect
                        name="payment_type"
                        control={control}
                        label={t("bonds.payment_type")}
                        options={bondsSettingsOPtions}
                        isMulti={false}
                        isDisabled={selectedId || isShow}
                        placeholder={t("bonds.payment_type")}
                        error={errors.payment_type}
                        defaultValue={[]}
                      />
                    </div>
                  </Col>
                </Row>
                <div style={{ display: "flex", gap: 20 }}>
                  <div className="mb-0">
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        disabled={isShow}
                        value=""
                        id="total"
                        {...register("total")}
                      />
                      <label className="form-check-label" htmlFor="total">
                        {t("common.total")}
                      </label>
                    </div>
                  </div>
                  <div className="mb-4">
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        disabled={isShow}
                        value=""
                        id="invalidCheck"
                        {...register("bond")}
                      />
                      <label
                        className="form-check-label"
                        htmlFor="invalidCheck"
                      >
                        {t("types.bonds.voucher")}
                      </label>
                      <div className="invalid-feedback">
                        {t("common.submit_error")}
                      </div>
                    </div>
                  </div>
                  <div className="form-check mb-3">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      value=""
                      id="sign"
                      disabled={isShow}
                      {...register("sign")}
                    />
                    <label className="form-check-label" htmlFor="sign">
                      {t("types.bonds.sign")}
                    </label>
                    <div className="invalid-feedback">
                      {t("common.submit_error")}
                    </div>
                  </div>
                </div>
              </>
            )}
            <div
              style={{
                display: "flex",
                alignItems: "start",
                gap: 8,
                justifyContent: "end",
              }}
            >
              <Button
                type="button"
                color="light"
                onClick={handelCancel}
                className="btn-sm "
                style={{ height: "32px", width: "54px" }}
              >
                {t("common.close")}
              </Button>
              {!isShow && (
                <Button
                  color="primary"
                  className="btn-sm waves-effect waves-light primary-button"
                  type="submit"
                  disabled={
                    isSubmitting ||
                    (!watch("titleArabic") && !watch("titleEnglish"))
                  }
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectedId ? (
                    t("common.update")
                  ) : (
                    t("common.add")
                  )}
                </Button>
              )}
            </div>
          </form>
        </Row>
        {/* </CardBody>
        </Card> */}
      </Container>
    </div>
  );
};
export default ActionBondsType;
