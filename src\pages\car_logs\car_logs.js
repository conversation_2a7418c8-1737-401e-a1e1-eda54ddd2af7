import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Modal,
  <PERSON>dalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Col,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import { carLogsAPis } from "../../apis/car-logs/api";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { carLogsQueries } from "../../apis/car-logs/query";
import CarLogsActions from "./action_car_logs";
import {
  formatDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import { useForm } from "react-hook-form";
import CustomInput from "../../components/Common/Input";
import { UsersQueries } from "../../apis/cars/query";
import { delegateQueries } from "../../apis/delegate/query";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import SearchCard from "../../components/Reports/search-card";
import { carLogsTypesQueries } from "../../apis/types/car-logs-types/query";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
import { FaTimes } from "react-icons/fa";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

// Custom component for kilometer comparison
const KilometerComparisonField = ({ control, hasButtonSearch = true }) => {
  const { t } = useTranslation();

  const comparisonOptions = [
    { label: t("common.greater_than"), value: 1 },
    { label: t("common.less_than"), value: 2 },
    { label: t("common.equal_to"), value: 3 },
  ];

  return (
    <Row className="g-2">
      <Col xs={6} className="pe-0">
        <CustomFilterSearch
          name="km_comparison"
          control={control}
          options={comparisonOptions}
          label={t("common.comparison")}
          hasButtonSearch={hasButtonSearch}
        />
      </Col>
      <Col xs={6}>
        <CustomInput
          name="km_value"
          control={control}
          type="number"
          label={t("common.km")}
        />
      </Col>
    </Row>
  );
};

const BondType = () => {
  const { pathname, search } = useLocation();
  const navigate = useNavigate();
  const queryParams = useMemo(() => new URLSearchParams(search), [search]);

  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openAddModel, setOpenAddModel] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [selectedCustomItemsForFilter, setSelectedCustomItemsForFilter] =
    useState(null);
  const { t, i18n } = useTranslation();
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const [limit, setLimit] = useState(50);
  const [searchParams, setSearchParams] = useState({});
  const [dateError, setDateError] = useState(null);

  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    setOpenAddModel(true);
  };
  const [page, setPage] = useState(1);

  const { control, reset, watch, setValue } = useForm({
    defaultValues: {
      cars: null,
      delegates: null,
      date_from: formatDate(today),
      date_to: formatDate(today),
      km_comparison: null,
      km_value: "",
    },
  });

  const {
    data: ContractTypes,
    isLoading: isLoadingCarLogs,
    refetch,
  } = carLogsQueries.useGetAll({
    limit: 50,
    page: page,
    searchParams,
  });

  useEffect(() => {
    // Only apply default filter if no search params exist and no URL params
    if (Object.keys(searchParams).length === 0 && !search) {
      const todayFormatted = formatDate(today);
      const defaultParams = {
        "filter[date][from]": todayFormatted,
        "filter[date][to]": todayFormatted,
      };
      setSearchParams(defaultParams);
    }
  }, []);

  // Get data for filter dropdowns
  const { data: carsData } = UsersQueries.useGetAllUsers({ status: 1 });
  const { data: delegatesData } = delegateQueries.useGetAll({ status: 1 });
  const { data: carLogsTypesData } = carLogsTypesQueries.useGetAll({
    status: 1,
  });

  // Convert data to options format
  const carOptions = useSetSelectOptions({
    data: carsData?.result,
    getOption: (item) => ({ label: item.name, value: item.id }),
  });

  const carLogsTypeOptions = useMemo(
    () =>
      carLogsTypesData?.result?.map((item) => ({
        label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
        value: item.id,
      })) || [],
    [carLogsTypesData?.result, i18n.language]
  );

  const delegateOptions = useSetSelectOptions({
    data: delegatesData?.result,
    getOption: (item) => ({ label: item.full_name, value: item.id }),
  });

  // Parse URL params to set initial form values
  const parseUrlParams = () => {
    try {
      // Initial values object
      const initialValues = {
        cars: null,
        delegates: null,
        date_from: formatDate(today),
        date_to: formatDate(today),
        km_comparison: null,
        km_value: "",
        logs_type_ids: null,
      };

      // Initialize params object for API call
      const params = {};

      // Parse car IDs
      const carIds = queryParams.getAll("car_ids[]");
      if (carIds.length > 0 && carOptions.length > 0) {
        initialValues.cars = carIds
          .map((id) =>
            carOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);

        // Add to params
        params["filter[car_ids][]"] = carIds;
      }

      // Parse delegate IDs
      const delegateIds = queryParams.getAll("delegate_ids[]");
      if (delegateIds.length > 0 && delegateOptions.length > 0) {
        initialValues.delegates = delegateIds
          .map((id) =>
            delegateOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);

        // Add to params
        params["filter[delegate_ids][]"] = delegateIds;
      }

      // Parse log type IDs
      const logTypeIds = queryParams.getAll("log_type_ids[]");
      if (logTypeIds.length > 0 && carLogsTypeOptions.length > 0) {
        initialValues.logs_type_ids = logTypeIds
          .map((id) =>
            carLogsTypeOptions.find(
              (option) => option.value.toString() === id.toString()
            )
          )
          .filter(Boolean);

        // Add to params
        params["filter[log_type_ids][]"] = logTypeIds;
      }

      // Parse date range
      const dateFrom = queryParams.get("date_from");
      if (dateFrom) {
        initialValues.date_from = dateFrom;
        params["filter[date][from]"] = dateFrom;
      }

      const dateTo = queryParams.get("date_to");
      if (dateTo) {
        initialValues.date_to = dateTo;
        params["filter[date][to]"] = dateTo;
      }

      // Parse KM filters
      const kmOperator = queryParams.get("km_operator");
      if (kmOperator && ["1", "2", "3"].includes(kmOperator)) {
        const operatorLabel = {
          gt: t("common.greater_than"),
          lt: t("common.less_than"),
          eq: t("common.equal_to"),
        };
        initialValues.km_comparison = {
          value: kmOperator,
          label: operatorLabel[kmOperator],
        };

        // Add to params
        params["filter[km][operator]"] = kmOperator;
      }

      const kmValue = queryParams.get("km_value");
      if (kmValue) {
        initialValues.km_value = kmValue;
        params["filter[km][value]"] = kmValue;
      }

      return { initialValues, params };
    } catch (error) {
      console.error("Error parsing URL parameters:", error);
      return {
        initialValues: {
          cars: null,
          delegates: null,
          date_from: formatDate(today),
          date_to: formatDate(today),
          km_comparison: null,
          km_value: "",
          logs_type_ids: null,
        },
        params: {},
      };
    }
  };

  // Initialize form after options are loaded
  useEffect(() => {
    if (
      carOptions.length > 0 &&
      delegateOptions.length > 0 &&
      carLogsTypeOptions.length > 0
    ) {
      const { initialValues, params } = parseUrlParams();
      reset(initialValues);

      // Set initial search params
      setSearchParams(params);
    }
  }, [
    carOptions.length,
    delegateOptions.length,
    carLogsTypeOptions.length,
    queryParams,
  ]);

  // Update URL with current filters
  const updateUrlWithFilters = (params) => {
    const newUrl = new URLSearchParams();

    // Add car IDs to URL
    if (params["filter[car_ids][]"]) {
      const carIds = Array.isArray(params["filter[car_ids][]"])
        ? params["filter[car_ids][]"]
        : [params["filter[car_ids][]"]];

      carIds.forEach((id) => newUrl.append("car_ids[]", id));
    }

    // Add delegate IDs to URL
    if (params["filter[delegate_ids][]"]) {
      const delegateIds = Array.isArray(params["filter[delegate_ids][]"])
        ? params["filter[delegate_ids][]"]
        : [params["filter[delegate_ids][]"]];

      delegateIds.forEach((id) => newUrl.append("delegate_ids[]", id));
    }

    // Add log type IDs to URL
    if (params["filter[log_type_ids][]"]) {
      const logTypeIds = Array.isArray(params["filter[log_type_ids][]"])
        ? params["filter[log_type_ids][]"]
        : [params["filter[log_type_ids][]"]];

      logTypeIds.forEach((id) => newUrl.append("log_type_ids[]", id));
    }

    // Add date range to URL - only if they differ from today's date
    const todayFormatted = formatDate(today);
    if (
      params["filter[date][from]"] &&
      params["filter[date][from]"] !== todayFormatted
    ) {
      newUrl.set("date_from", params["filter[date][from]"]);
    }

    if (
      params["filter[date][to]"] &&
      params["filter[date][to]"] !== todayFormatted
    ) {
      newUrl.set("date_to", params["filter[date][to]"]);
    }

    // Add KM filters to URL
    if (params["filter[km][operator]"]) {
      newUrl.set("km_operator", params["filter[km][operator]"]);
    }

    if (params["filter[km][value]"]) {
      newUrl.set("km_value", params["filter[km][value]"]);
    }

    // Replace current URL without reloading the page
    navigate(`${pathname}?${newUrl.toString()}`, { replace: true });
  };

  const searchFields = watch([
    "cars",
    "delegates",
    "date_from",
    "date_to",
    "km_comparison",
    "km_value",
    "logs_type_ids",
  ]);

  // Add a manual search function instead of relying on the useEffect
  const handleManualSearch = () => {
    // If date validation error exists, don't update search params
    if (dateError) {
      return;
    }

    const params = {};

    // Check if cars multi-select has values
    if (searchFields[0]) {
      // Handle both single object and array of objects
      if (Array.isArray(searchFields[0])) {
        if (searchFields[0].length > 0) {
          // Create separate parameters for each car ID
          searchFields[0].forEach((car) => {
            // Use array notation with square brackets to send multiple values with same key
            if (!params["filter[car_ids][]"]) {
              params["filter[car_ids][]"] = [car.value];
            } else {
              params["filter[car_ids][]"].push(car.value);
            }
          });
        }
      } else if (searchFields[0].value) {
        // Single object case
        params["filter[car_ids][]"] = [searchFields[0].value];
      }
    }

    // Check if delegates multi-select has values
    if (searchFields[1]) {
      // Handle both single object and array of objects
      if (Array.isArray(searchFields[1])) {
        if (searchFields[1].length > 0) {
          // Create separate parameters for each delegate ID
          searchFields[1].forEach((delegate) => {
            if (!params["filter[delegate_ids][]"]) {
              params["filter[delegate_ids][]"] = [delegate.value];
            } else {
              params["filter[delegate_ids][]"].push(delegate.value);
            }
          });
        }
      } else if (searchFields[1].value) {
        // Single object case
        params["filter[delegate_ids][]"] = [searchFields[1].value];
      }
    }

    // Date filters - only add if they're not empty and not the default today's date
    if (searchFields[2]) {
      params["filter[date][from]"] = searchFields[2];
    }

    if (searchFields[3]) {
      params["filter[date][to]"] = searchFields[3];
    }

    // KM comparison filters
    if (searchFields[4] && searchFields[5]) {
      params["filter[km][operator]"] = searchFields[4].value;
      params["filter[km][value]"] = searchFields[5];
      setSelectedCustomItemsForFilter({
        operation: watch("km_comparison")?.label,
        value: watch("km_value"),
      });
    }

    // Handle logs_type_ids multi-select
    if (searchFields[6]) {
      // Handle both single object and array of objects
      if (Array.isArray(searchFields[6])) {
        if (searchFields[6].length > 0) {
          // Create separate parameters for each logs type ID
          searchFields[6].forEach((logType) => {
            if (!params["filter[log_type_ids][]"]) {
              params["filter[log_type_ids][]"] = [logType.value];
            } else {
              params["filter[log_type_ids][]"].push(logType.value);
            }
          });
        }
      }
    }

    // Update search params
    setSearchParams(params);
    // Update URL with new filters
    // updateUrlWithFilters(params);
    // Reset to first page
    setPage(1);
    // Trigger a refetch with new parameters
    refetch();
  };

  const handleReset = () => {
    // Reset with empty arrays
    reset({
      cars: null,
      delegates: null,
      date_from: formatDate(today),
      date_to: formatDate(today),
      km_comparison: null,
      km_value: "",
      logs_type_ids: null,
    });

    // Explicitly clear km fields using setValue
    setValue("km_value", "");
    setValue("km_comparison", null);

    // Clear date validation errors
    setDateError(null);
    // Clear search params completely
    setSearchParams({});
    // Clear custom filter items (km filter badge)
    setSelectedCustomItemsForFilter(null);
    // Clear URL params
    navigate(pathname, { replace: true });
    // Reset page to 1
    setPage(1);
    refetch();
  };

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setOpenAddModel(false);
    setIsShow(false);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("cars.car_logs"),
      link: pathname,
    },
  ];

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.car"),
      accessor: "car",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("cars.car_log_type"),
      accessor: "car_log_type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.car_logs.car_logs_date"),
      accessor: "car_logs_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.car_logs.car_logs_time"),
      accessor: "car_logs_time",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.km"),
      accessor: "km",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("cars.payment_amount"),
      accessor: "total_price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"car_log.update"}>
              {!cellProps.bond && (
                <Link
                  title={"Disabled"}
                  // to={`/action-car_logs?id=${cellProps.id}`}
                  className="text-primary"
                  onClick={() => {
                    setOpenAddModel(true);
                    handelSelectId(cellProps.id);
                  }}
                >
                  {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                  <FaPenToSquare size={14} />
                </Link>
              )}
            </Can>
            <Can permission={"car_log.destroy"}>
              {cellProps.is_default !== 1 && (
                <Link
                  onClick={() => {
                    if (cellProps.isDefault !== 1) {
                      handelOpenModal();
                      handelSelectId(cellProps.id);
                    }
                  }}
                  to="#"
                  className="text-danger"
                >
                  {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                  <MdDeleteSweep size={18} />
                </Link>
              )}
            </Can>
            <Can permission={"car_log.show"}>
              <Link
                onClick={() => {
                  handelSelectId(cellProps.id);
                  setIsShow(true);
                  setOpenAddModel(true);
                }}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              car: item?.car?.name || "---",
              car_log_type:
                i18n.language === "eng"
                  ? item?.car_log_type?.title?.en
                  : item?.car_log_type?.title?.ar || "---",
              km: item.km || 0,
              delegate: item?.delegate?.full_name || "---",
              total_price: item?.total_price || 0,
              bond: item?.bond,
              car_logs_date:
                new Date(item?.created_at)?.toISOString()?.split("T")[0] ||
                "---",
              car_logs_time:
                new Date(item?.created_at)
                  ?.toISOString()
                  ?.split("T")[1]
                  ?.split(".")[0] || "---",
            }))
            .reverse()
        : [],
    [ContractTypes?.result, i18n.language, page]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await carLogsAPis.deleteUser({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
    // Call API with selected permissions (data.permissions)
  };

  // Validate date_from and date_to whenever they change
  useEffect(() => {
    const date_from = searchFields[2];
    const date_to = searchFields[3];

    if (date_from && date_to) {
      const fromDate = new Date(date_from);
      const toDate = new Date(date_to);

      if (fromDate > toDate) {
        setDateError(t("common.date_error"));
        toastr.error(
          t("common.date_error") || "Date from cannot be greater than date to"
        );
      } else {
        setDateError(null);
      }
    }
  }, [searchFields[2], searchFields[3], t]);

  // Filter fields configuration for SearchCard
  const SearchData = [
    {
      id: 1,
      label: t("common.car"),
      type: "select",
      name: "cars",
      options: carOptions,
      isMulti: true,
      cols: 1,
      hasButtonSearch: true,
    },
    {
      id: 2,
      label: t("common.delegate"),
      type: "select",
      name: "delegates",
      options: delegateOptions,
      isMulti: true,
      cols: 1,
      hasButtonSearch: true,
    },
    {
      id: 3,
      label: t("cars.car_log_type"),
      type: "select",
      name: "logs_type_ids",
      options: carLogsTypeOptions,
      isMulti: true,
      cols: 2,
      hasButtonSearch: true,
    },
  ];

  // Input fields configuration for SearchCard
  const inputsArray = [
    {
      id: 3,
      name: "date_from",
      type: "date",
      label: t("common.date_from"),
      cols: 2,
      error: dateError ? { message: dateError } : null,
    },
    {
      id: 4,
      name: "date_to",
      type: "date",
      label: t("common.date_to"),
      cols: 2,
      error: dateError ? { message: dateError } : null,
    },
  ];
  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("cars.car_logs")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("cars.car_logs")}
          isAddOptions
          handleOrderClicks={handelAddBonds}
          canPermission={"car_log.store"}
        />
        <Card
          style={{
            maxHeight: "90%",
            height: "90%",
            overflowY: "auto",
            padding: 20,
          }}
        >
          {/* <CardBody> */}
          <TableContainer
            customComponent={
              <div className="d-grid gap-2 w-100">
                <SearchCard
                  SearchData={SearchData}
                  control={control}
                  hadelReset={handleReset}
                  inputsArray={inputsArray}
                  watch={watch}
                  setValue={setValue}
                  handelSearch={handleManualSearch}
                  hasButtonSearch={true}
                  customComponents={
                    <Col xs={4}>
                      <div className="mb-2 ms-1">
                        <KilometerComparisonField
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          hasButtonSearch={true}
                        />
                      </div>
                    </Col>
                  }
                  customItems={
                    selectedCustomItemsForFilter?.operation && (
                      <div
                        className="badge border d-flex g-2 align-items-center px-3 py-2"
                        style={{
                          background: "#0d6efd",
                          color: "#fff",
                        }}
                      >
                        <span className="me-3">
                          {selectedCustomItemsForFilter?.operation}
                        </span>
                        <span className="me-3">
                          {selectedCustomItemsForFilter?.value}
                        </span>
                        <FaTimes
                          role="button"
                          style={{
                            fontSize: 12,
                            color: "#fff",
                          }}
                          onClick={() => {
                            setSelectedCustomItemsForFilter(null);
                            setValue("km_value", "");
                            setValue("km_comparison", null);
                          }}
                        />
                      </div>
                    )
                  }
                />
              </div>
            }
            hideSHowGFilter={false}
            columns={columns || []}
            data={rowData || []}
            isPagination={true}
            iscustomPageSize={true}
            isBordered={true}
            pageSize={10}
            pageIndex={page}
            manualPagination={true}
            pageCount={ContractTypes?.meta?.last_page || 1}
            currentPage={page}
            setPage={setPage}
            setLimit={setLimit}
            isLoading={isLoadingCarLogs}
            hasTotal
            total={ContractTypes?.meta?.total_price}
          />
          {/* </CardBody> */}
        </Card>
      </Container>
      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCLoseModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCLoseModal}>
          {t("common.delete")} {t("cars.car_logs")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button
              type="button"
              color="light"
              onClick={handelCLoseModal}
              className="btn-sm"
            >
              {t("common.close")}
            </Button>
            <Button
              disabled={isDeleting}
              onClick={DeleteFun}
              type="button"
              color="danger"
              className="btn-sm"
            >
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>

      <Modal isOpen={openAddModel} toggle={handelCLoseModal} backdrop="static">
        <ModalHeader toggle={handelCLoseModal}>
          {isShow
            ? t("common.show") + " " + t("cars.car_logs")
            : selectId
            ? t("common.update") + " " + t("cars.car_logs")
            : t("common.add") + " " + t("cars.car_logs")}
        </ModalHeader>
        <ModalBody>
          {isLoadingCarLogs ? (
            <div className="container-loading">
              <ClipLoader color="#ddd" size={50} />
            </div>
          ) : (
            <CarLogsActions
              isShow={isShow}
              selectId={selectId}
              handelCLose={handelCLoseModal}
              refetch={refetch}
            />
          )}
        </ModalBody>
      </Modal>
    </div>
  );
};
export default BondType;
