import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  <PERSON><PERSON>,
  Col,
  Container,
  Label,
  Modal,
  ModalBody,
  ModalFooter,
  <PERSON>dalHeader,
  <PERSON>,
} from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState, useRef } from "react";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { offerAPis } from "../../apis/offer-price/api";
import { clientsQueries } from "../../apis/clients/query";
import { useForm, useWatch } from "react-hook-form";
// import Select from "react-select";
import { productQueries } from "../../apis/products/query";
import TableContainer from "../../components/Common/TableContainer";
import { OfferQueries } from "../../apis/offer-price/query";
import {
  endDate,
  formatDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import CustomInput from "../../components/Common/Input";
import { FormField } from "../bills/action-bill";
import CustomSelect from "../../components/Common/Select";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import ActionSection from "../../components/Common/ActionSection";
import TextAreaField from "../../components/Common/textArea";
import { MdDeleteSweep } from "react-icons/md";
import { FaPenToSquare } from "react-icons/fa6";

const OfferActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [search, setSearch] = useSearchParams();
  const [optionClients, setOptionClients] = useState([]); // Assuming you have a way to populate this
  const [productList, setProductList] = useState([]);
  const [clientsList, setClientsList] = useState([]);
  const [selectedProductId, setSelectedProductId] = useState(0);
  const [selectedTotalPrice, setSelectedTotalPrice] = useState(0);
  const { t } = useTranslation();

  const { pathname } = useLocation();

  const isShow = search.get("id") && search.get("id")?.includes("Show");
  const currentShow = search.get("id")?.includes("Show");
  const isDuplicate = search.get("id")?.includes("idDuplicate");

  const { data, isLoading } = OfferQueries.useGetOne({
    id: Number(selectId),
  });

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const { data: products } = productQueries.useGetAll({ status: 1 });

  const breadcrumbItems = [
    { title: t("offer.quotation"), link: "/offer-price" },
    {
      title: currentShow
        ? t("common.show")
        : selectId
        ? t("common.update")
        : t("common.create"),
      link: pathname,
    },
  ];

  const navigate = useNavigate();

  const handelCancel = () => {
    navigate(-1);
    reset();
  };

  const schema = yup
    .object({
      start_date: yup.string().required(t("common.field_required")),
      end_date: yup.string().required(t("common.field_required")),
      // start_date: yup.string().required("start date is required"),
      clients: yup
        .object()
        .shape({
          value: yup.string().required(t("common.value_required")),
          label: yup.string().required(t("common.label_required")),
        })
        .nullable() // Handle cases where no value is selected
        .required(t("common.field_required")),
    })
    .required();

  const initialSTate = {
    clients: null,
    end_date: formatDate(endDate),
    start_date: formatDate(today),
    total: null,
    discount_percentage: null,
    discount_value: null,
    quant: null,
    product_id: null,
    product_note: "",
    product_gift: null,
    product_total: null,
    unit_price: null,
    discount_product: null,
    total_discounts: null,
    total_discount_percentage: null,
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
    watch,
    trigger,
    clearErrors,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const handelOpenAddModal = () => {
    setOpenAddModal(true);
  };
  const handelCloseAddModal = () => {
    setOpenAddModal(false);
    setValue("product_id", null);
    setValue("quant", "");
    setValue("product_note", "");
    setValue("maintaince_quant", "");
    setValue("change_oil", "");
    setValue("product_gift", "");
    setValue("product_total", "");
    setValue("return_date", formatDate(today));
    setValue("product_price", "");
    setValue("unit_price", "");
    setValue("discount_product", "");
    setValue("discount_product_type", "");
    setValue("product_discount_value", "");
    setValue("product_discount_percentage", "");
  };

  const resetInputs = () => {
    setValue("product_id", null);
    setValue("quant", "");
    setValue("product_note", "");
    setValue("change_oil", 1);
    setValue("product_gift", "");
    setValue("product_total", "");
    setValue("return_date", "");
    setValue("unit_price", null);
    setValue("discount_product", "");
    setValue("discount_percentage", null);
    setValue("discount_value", null);
    setValue("discount_product_type", "");
    setValue("product_discount_value", "");
    setValue("product_discount_percentage", "");
    setSelectedProductId(0);
  };

  useEffect(() => {
    // Trigger validation when the form is first rendered (if necessary)
    trigger("quant"); // Trigger validation for the "quant" field
  }, [trigger]); // This ensures validation is triggered on mount

  useEffect(() => {
    if (watch("product_id")?.value) {
      // const product = products.result.find(
      //   (item) => item.id === watch("product_id")?.value
      // );

      if (Number(watch("discount_product")) < 0) {
        setError("discount_product", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
      }
      if (Number(watch("product_total")) < 0) {
        setError("product_total", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
      }
      if (Number(watch("product_gift")) < 0) {
        setError("product_gift", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
      }
    }
  }, [watch("quant"), watch("product_id"), openAddModal, watch("unit_price")]);

  useEffect(() => {
    if (watch("unit_price") && watch("quant")) {
      const newProductTotal =
        Number(watch("unit_price")) * Number(watch("quant"));
      setValue("product_total", newProductTotal);

      // If we're editing a product, update the productList and recalculate selectedTotalPrice
      if (selectedProductId) {
        setProductList((prev) => {
          const updatedList = prev.map((item) => {
            if (item.product_id === selectedProductId) {
              const discountValue =
                Number(watch("product_discount_value")) || 0;
              const discountPercentage =
                Number(watch("product_discount_percentage")) || 0;
              const netPrice =
                discountValue > 0
                  ? newProductTotal - discountValue
                  : newProductTotal;

              return {
                ...item,
                quant: Number(watch("quant")),
                total: newProductTotal,
                unit_price: Number(watch("unit_price")),
                net_price: netPrice,
                discount_value: discountValue,
                discount_percentage: discountPercentage,
                discount: discountValue,
              };
            }
            return item;
          });
          return updatedList;
        });
      }
    }
  }, [watch("unit_price"), watch("quant"), selectedProductId]);

  const fieldsNames = [
    {
      name: "start_date",
      isRequired: false,
      errorMessage: errors?.start_date?.message,
      label: t("common.start_date"),
      type: "date",
      showIn: true,
      disabled: currentShow,
    },
    {
      name: "end_date",
      isRequired: false,
      errorMessage: errors?.end_date?.message,
      label: t("common.end_date"),
      type: "date",
      showIn: true,
      disabled: currentShow,
    },
  ];

  const totalsFields = [
    {
      name: "total",
      isRequired: false,
      errorMessage: errors?.total?.message,
      label: t("common.rows_total"),
      type: "number",
      disabled: true,
      showIn: true,
    },
    {
      name: "total_discounts",
      isRequired: false,
      errorMessage: errors?.total_discounts?.message,
      label: t("common.rows_discount"),
      type: "number",
      showIn: true,
      disabled: true,
    },
  ];

  const productsOptions = useSetSelectOptions({
    data: products?.result,
    getOption: (item) => ({
      label: `${item.name}`,
      value: item.id,
    }),
  });

  const productDiscountPercentage = useWatch({
    control,
    name: "product_discount_percentage",
  });
  const productDiscountValue = useWatch({
    control,
    name: "product_discount_value",
  });
  const productTotal = useWatch({ control, name: "product_total" });
  const lastProductDiscountUpdate = useRef(null);

  useEffect(() => {
    if (lastProductDiscountUpdate.current === "value") return;
    const percentage = Number(productDiscountPercentage);
    const totalValue = Number(productTotal);
    if (!percentage && percentage !== 0) return;
    if (percentage >= 0 && percentage <= 100 && totalValue > 0) {
      const newValue = Math.round((percentage * totalValue) / 100);
      lastProductDiscountUpdate.current = "percentage";
      setValue("product_discount_value", newValue > 0 ? newValue : null, {
        shouldValidate: true,
      });

      // If we're editing a product, update the productList
      if (selectedProductId) {
        setProductList((prev) => {
          const updatedList = prev.map((item) => {
            if (item.product_id === selectedProductId) {
              const discountValue = newValue > 0 ? newValue : 0;
              const netPrice =
                discountValue > 0 ? totalValue - discountValue : totalValue;

              return {
                ...item,
                discount_value: discountValue,
                discount_percentage: percentage,
                discount: discountValue,
                net_price: netPrice,
              };
            }
            return item;
          });
          return updatedList;
        });
      }
    }
  }, [productDiscountPercentage, selectedProductId, productTotal]);
  useEffect(() => {
    if (lastProductDiscountUpdate.current === "percentage") return;
    const value = Number(productDiscountValue);
    const totalValue = Number(productTotal);
    if (!value && value !== 0) return;
    if (value >= 0 && totalValue > 0) {
      const newPercentage = Math.round((value / totalValue) * 100);
      lastProductDiscountUpdate.current = "value";
      setValue(
        "product_discount_percentage",
        newPercentage > 0 ? newPercentage : null,
        { shouldValidate: true }
      );

      // If we're editing a product, update the productList
      if (selectedProductId) {
        setProductList((prev) => {
          const updatedList = prev.map((item) => {
            if (item.product_id === selectedProductId) {
              const discountValue = value;
              const netPrice =
                discountValue > 0 ? totalValue - discountValue : totalValue;

              return {
                ...item,
                discount_value: discountValue,
                discount_percentage: newPercentage > 0 ? newPercentage : 0,
                discount: discountValue,
                net_price: netPrice,
              };
            }
            return item;
          });
          return updatedList;
        });
      }
    }
  }, [productDiscountValue, selectedProductId, productTotal]);
  useEffect(() => {
    const timeout = setTimeout(() => {
      lastProductDiscountUpdate.current = null;
    }, 50);
    return () => clearTimeout(timeout);
  }, [productDiscountPercentage, productDiscountValue]);

  const totalDiscountPercentage = useWatch({
    control,
    name: "total_discount_percentage",
  });
  const totalDiscountValue = useWatch({
    control,
    name: "total_discount_value",
  });
  const total = useWatch({ control, name: "total" });
  const totalDiscountsRows = useWatch({ control, name: "total_discounts" });
  const lastGeneralDiscountUpdate = useRef(null);

  useEffect(() => {
    if (lastGeneralDiscountUpdate.current === "value") return;
    const percentage = Number(totalDiscountPercentage);
    const totalValue = Number(total) - Number(totalDiscountsRows);
    if (!percentage && percentage !== 0) return;
    if (percentage >= 0 && percentage <= 100 && totalValue > 0) {
      const newValue = Math.round((percentage * totalValue) / 100);
      lastGeneralDiscountUpdate.current = "percentage";
      setValue("total_discount_value", newValue > 0 ? newValue : null, {
        shouldValidate: true,
      });
    }
  }, [totalDiscountPercentage]);
  useEffect(() => {
    if (lastGeneralDiscountUpdate.current === "percentage") return;
    const value = Number(totalDiscountValue);
    const totalValue = Number(total) - Number(totalDiscountsRows);
    if (!value && value !== 0) return;
    if (value >= 0 && totalValue > 0) {
      const newPercentage = Math.round((value / totalValue) * 100);
      lastGeneralDiscountUpdate.current = "value";
      setValue(
        "total_discount_percentage",
        newPercentage > 0 ? newPercentage : null,
        { shouldValidate: true }
      );
    }
  }, [totalDiscountValue]);
  useEffect(() => {
    const timeout = setTimeout(() => {
      lastGeneralDiscountUpdate.current = null;
    }, 100);
    return () => clearTimeout(timeout);
  }, [totalDiscountValue, totalDiscountPercentage]);

  const productsFields = [
    {
      id: 0,
      field: (
        <Col xs={6} className="mb-2">
          <CustomSelect
            control={control}
            error={errors.product_id}
            label={t("common.product")}
            options={productsOptions?.filter(
              (option) =>
                !productList.some(
                  (product) => product.product_id === option.value
                )
            )}
            name="product_id"
            isDisabled={isShow}
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 1,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            error={errors?.quant}
            placeholder={t("common.quant")}
            name="quant"
            isDisabled={!watch("product_id")?.value}
            label={t("common.quant")}
            type="number"
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 2,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            error={errors?.unit_price}
            placeholder={t("common.price")}
            name="unit_price"
            label={t("common.price")}
            type="number"
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 3,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            error={errors?.product_gift}
            name="product_gift"
            label={t("common.gift")}
            type="number"
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 4,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            error={errors?.product_total}
            name="product_total"
            label={t("common.total")}
            type="number"
          />
        </Col>
      ),
      showIn: true,
    },

    {
      id: 7,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            name="product_discount_value"
            isDisabled={isShow}
            placeholder={t("bills.discount_value")}
            type="number"
            error={errors?.product_discount_value}
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 6,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            name="product_discount_percentage"
            isDisabled={isShow}
            placeholder={t("bills.discount_percentage")}
            type="number"
            error={errors?.product_discount_percentage}
          />
        </Col>
      ),
      showIn: true,
    },
  ];

  const handelAddProductToList = () => {
    try {
      const productId = watch("product_id")?.value;
      const quant = Number(watch("quant"));
      const unitPrice = Number(watch("unit_price"));
      const productTotal = Number(watch("product_total")) || 0;
      const productGift = Number(watch("product_gift"));
      const discountValue = Number(watch("product_discount_value")) || 0;
      const discountPercentage =
        Number(watch("product_discount_percentage")) || 0;
      clearErrors();
      // product_id validation
      if (!productId) {
        setError("product_id", {
          type: "manual",
          message: t("common.field_required"),
        });
        return;
      }
      // quant validation
      if (quant <= 0) {
        setError("quant", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }
      if (quant > 5000) {
        setError("quant", {
          type: "manual",
          message: t("bills.validation.greater_than_5000"),
        });
        return;
      }
      // unit_price validation
      if (unitPrice <= 0) {
        setError("unit_price", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }
      // product_total validation
      if (productTotal <= 0) {
        setError("product_total", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }
      // product_gift validation
      if (productGift < 0) {
        setError("product_gift", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }
      if (productGift > 5000) {
        setError("product_gift", {
          type: "manual",
          message: t("bills.validation.greater_than_5000"),
        });
        return;
      }
      // discount_value validation
      if (discountValue < 0) {
        setError("product_discount_value", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }
      if (discountValue > productTotal) {
        setError("product_discount_value", {
          type: "manual",
          message: t("bills.discount_validation"),
        });
        return;
      }
      // discount_percentage validation
      if (discountPercentage < 0) {
        setError("product_discount_percentage", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }
      if (discountPercentage > 100) {
        setError("product_discount_percentage", {
          type: "manual",
          message: t("bills.validation.max_percentage_value"),
        });
        return;
      }
      // NEW: discount_percentage as value must not exceed product_total
      if ((discountPercentage / 100) * productTotal > productTotal) {
        setError("product_discount_percentage", {
          type: "manual",
          message: t("bills.discount_validation"),
        });
        return;
      }
      // NEW: discount_value must not exceed product_total (already checked above)
      // باقي الكود كما هو
      if (productId && quant > 0) {
        const productNote = watch("product_note");
        const netPrice =
          discountValue > 0
            ? Number(productTotal) - discountValue
            : Number(productTotal);
        const newProduct = {
          product_id: productId,
          product_name:
            productsOptions.find((item) => item.value === productId)?.label ||
            productId.label,
          quant: quant,
          note: productNote,
          gift: productGift,
          total: productTotal,
          unit_price: unitPrice,
          net_price: netPrice,
          discount_value: discountValue,
          discount_percentage: discountPercentage,
          discount: discountValue,
        };

        setProductList((prev) => {
          const existingIndex = prev.findIndex(
            (item) => item.product_id === selectedProductId
          );
          if (existingIndex !== -1) {
            const updatedList = [...prev];
            updatedList[existingIndex] = newProduct;
            return updatedList;
          } else {
            return [...prev, newProduct];
          }
        });
        resetInputs();
        setOpenAddModal(false);
        setIsUpdate(false);
      }
    } catch (error) {
      console.log("Error handling product:", error);
    }
  };

  const handelSetUpdate = (id) => {
    setOpenAddModal(true);
    setSelectedProductId(id);
    const currentProduct = productList.find((item) => item.product_id === id);

    setValue("product_id", {
      value: currentProduct.product_id,
      label: currentProduct.product_name,
    });

    setValue("quant", currentProduct.quant);
    setValue("product_note", currentProduct.note);
    setValue("product_gift", currentProduct.gift);
    setValue("product_total", currentProduct.total);
    setValue("unit_price", currentProduct.unit_price); // Fixed: use unit_price instead of net_price
    setValue("change_oil", currentProduct.change_oil);
    setValue("product_discount_value", currentProduct.discount_value);
    setValue("product_discount_percentage", currentProduct.discount_percentage);
  };

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      const returnedProduct = [];
      data?.result.offer_details.map((item) => {
        returnedProduct.push({
          product_id: { label: item.product_name, value: item.product_id },
          product_name: item.product_name,
          quant: item.quant,
          gift: item.gift,
          total: item.total,
          unit_price: item.unit_price,
          discount_value: item.discount_value,
          discount_percentage: item.discount_percentage,
          note: item.notes,
          discount: item.discount_value,
          net_price:
            item.discount_value > 0
              ? item.total - item.discount_value
              : item.discount_percentage > 0
              ? item.total - (item.total * item.discount_percentage) / 100
              : item.total,
        });
      });
      setProductList(returnedProduct);

      // Populate form with role data when loaded
      reset({
        total: data?.result.total,
        total_discount_percentage: data?.result.discount_percentage,
        total_discount_value: data?.result.discount_value,
        total_discount:
          data?.result.discount_value > 0
            ? data?.result.discount_value
            : data?.result.discount_percentage,
        start_date: data?.result.start_date,
        clients: {
          label: data?.result.clients[0]?.full_name
            ? data?.result.clients[0]?.full_name
            : "---" + "/" + data?.result.clients[0]?.company_name
            ? data?.result.clients[0]?.company_name
            : "---",
          value: data?.result.clients[0]?.id,
        },
        end_date: data?.result.end_date,
      });
    }
  }, [selectId, isLoading, data]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      if (productList.length === 0) {
        toastr.error(t("offer.product_validation"));
        window.scrollTo(0, document.body.scrollHeight);
        return;
      }
      // Validation for total_discount_value and total_discount_percentage
      const totalDiscountValue = Number(data.total_discount_value) || 0;
      const totalDiscountPercentage =
        Number(data.total_discount_percentage) || 0;
      if (totalDiscountValue > selectedTotalPrice) {
        setError("total_discount_value", {
          type: "manual",
          message: t("bills.discount_validation"),
        });
        return;
      }
      if (
        (totalDiscountPercentage / 100) * selectedTotalPrice >
        selectedTotalPrice
      ) {
        setError("total_discount_percentage", {
          type: "manual",
          message: t("bills.discount_validation"),
        });
        return;
      }
      const modifiedProductList = productList.map((product) => {
        const productId =
          typeof product.product_id === "object" && product.product_id.value
            ? product.product_id.value
            : product.product_id;
        return {
          ...product,
          product_id: productId,
        };
      });
      const dataToSend = {
        products: modifiedProductList,
        start_date: data.start_date,
        end_date: data.end_date,
        discount_percentage: Number(data.total_discount_percentage) || 0,
        discount_value: Number(data.total_discount_value) || 0,
        clients: [data.clients.value],
        total: selectedTotalPrice,
      };
      const response = await offerAPis.update({
        payload: dataToSend,
        id: selectId,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
      setProductList([]);
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    try {
      if (productList.length === 0) {
        toastr.error(t("offer.product_validation"));
        window.scrollTo(0, document.body.scrollHeight);
        return;
      }
      // Validation for total_discount_value and total_discount_percentage
      const totalDiscountValue = Number(data.total_discount_value) || 0;
      const totalDiscountPercentage =
        Number(data.total_discount_percentage) || 0;
      if (totalDiscountValue > selectedTotalPrice) {
        setError("total_discount_value", {
          type: "manual",
          message: t("bills.discount_validation"),
        });
        return;
      }
      if (
        (totalDiscountPercentage / 100) * selectedTotalPrice >
        selectedTotalPrice
      ) {
        setError("total_discount_percentage", {
          type: "manual",
          message: t("bills.discount_validation"),
        });
        return;
      }
      const modifiedProductList = productList.map((product) => {
        const productId =
          typeof product.product_id === "object" && product.product_id.value
            ? product.product_id.value
            : product.product_id;
        return {
          ...product,
          product_id: productId,
        };
      });
      const dataToSend = {
        products: modifiedProductList,
        start_date: data.start_date,
        end_date: data.end_date,
        discount_percentage: Number(data.total_discount_percentage) || 0,
        discount_value: Number(data.total_discount_value) || 0,
        clients: [data.clients.value],
        total: selectedTotalPrice,
      };
      const response = await offerAPis.add({ payload: dataToSend });
      toastr.success(response.message);
      navigate(-1);
      reset();
      setProductList([]);
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };
  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.total"),
      accessor: "total",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.gift"),
      accessor: "gift",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.price"),
      accessor: "unit_price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.discount"),
      accessor: "discount_value",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.discount") + "%",
      accessor: "discount_percentage",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.net_price"),
      accessor: "net_price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          !currentShow && (
            <div className="d-flex align-items-center gap-2 justify-content-start">
              <div
                className="text-primary"
                onClick={() => {
                  handelSetUpdate(cellProps.product_id);
                  setIsUpdate(true);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </div>
              <div
                onClick={() => {
                  handelFilterFromProductList(cellProps.product_id);
                }}
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </div>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];
  const rowData = useMemo(
    () =>
      productList.length > 0
        ? productList
            .map((item, index) => ({
              product_id: item.product_id,
              id_toShow: index + 1,
              product_name: item.product_name,
              quant: item.quant,
              gift: item.gift || "----",
              unit_price: item.unit_price,
              total: item.total,
              product_note: item.note || " ",
              discount_value: `${item.discount_value}`,
              discount_percentage: `${item.discount_percentage}%`,
              net_price: `${item.net_price}`,
            }))
            .reverse()
        : [],
    [productList]
  );

  useEffect(() => {
    if (productList.length > 0) {
      let currentTotal = 0;
      let currentDiscount = 0;
      productList.forEach((item) => {
        currentTotal += Number(item.total) || 0;
        currentDiscount += Number(item.discount) || 0;
      });
      setValue("total", currentTotal);
      setValue("total_discounts", currentDiscount);
      // خصم عام
      const generalDiscount = Number(watch("total_discount_value")) || 0;
      setSelectedTotalPrice(currentTotal - currentDiscount - generalDiscount);
    } else {
      setValue("total", 0);
      setValue("total_discounts", 0);
      setSelectedTotalPrice(0);
    }
  }, [productList, watch("total_discount_value")]);

  const handelFilterFromProductList = (id) => {
    // استخراج القيمة الحقيقية للـ id سواء كان كائن أو رقم
    const idValue =
      typeof id === "object" && id !== null && "value" in id ? id.value : id;

    const updatedProductList = productList.filter((item) => {
      // استخراج القيمة الحقيقية للمنتج
      const productId =
        typeof item.product_id === "object" &&
        item.product_id !== null &&
        "value" in item.product_id
          ? item.product_id.value
          : item.product_id;

      return productId !== idValue;
    });

    setProductList(updatedProductList);
  };

  useEffect(() => {
    if (clients?.result?.length > 0) {
      setOptionClients(
        clients.result
          .filter(
            (client) => !clientsList.some((item) => item.value === client.id)
          )
          .map((item) => ({
            label: `${item.company_name || "---"}/${item.full_name || "---"}`,
            value: item.id,
          }))
      );
    }
  }, [clients?.result, clientsList]);

  useEffect(() => {
    if (openAddModal && !isUpdate) {
      const _selectedProductId = products?.result?.find(
        (item) => item.id === watch("product_id")?.value
      );
      setIsUpdate(false);
      setValue("unit_price", _selectedProductId?.price);
    }
  }, [openAddModal, watch("product_id"), isUpdate]);

  const [opeAcceptModal, setOpeAcceptModal] = useState(false);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setOpeAcceptModal(false);
  };

  const [isAccepted, setIsAccepted] = useState(false);
  const [isRejected, setIsRejected] = useState(false);

  const rejectStatus = async () => {
    try {
      setIsRejected(true);
      clearErrors();
      const response = await offerAPis.Approve({
        id: selectId,
        status: 2,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
      setIsRejected(false);
      handelCLoseModal();
      setOpenAddModal(false);
    } catch (error) {
      setIsRejected(false);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const handelAcceptRequest = async () => {
    try {
      setIsAccepted(true);
      const response = await offerAPis.Approve({
        id: selectId,
        status: 1,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
      setIsAccepted(false);
      handelCLoseModal();
      setOpenAddModal(false);
    } catch (error) {
      handleBackendErrors({ error, setError });
      setIsAccepted(false);
    }
  };
  const clientOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: `${item.company_name}/${item.full_name || "---"}`,
      value: item.id,
    }),
  });

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("offer.quotation")}
          breadcrumbItems={breadcrumbItems}
        />
        <ActionSection
          isLoading={isLoading}
          handleSubmit={handleSubmit}
          selectId={selectId}
          UpdateFun={selectId && !isShow && !isDuplicate ? UpdateFun : addFun}
          customText={
            isSubmitting ? (
              <ClipLoader color="white" size={15} />
            ) : selectId ? (
              isDuplicate ? (
                t("common.save")
              ) : (
                t("common.update")
              )
            ) : (
              t("common.add")
            )
          }
          addFun={addFun}
          handelCancel={handelCancel}
          isShow={isDuplicate ? false : isShow}
          isSubmitting={isSubmitting}
        >
          <Row>
            <div style={{ display: "flex", marginBottom: 10, gap: 10 }}>
              {fieldsNames.map(
                (field) =>
                  field.showIn && (
                    <FormField
                      field={field}
                      register={register}
                      errors={errors}
                      isShow={isShow}
                      t={t}
                      control={control}
                      placeholder={field.label}
                    />
                  )
              )}
              <CustomSelect
                control={control}
                error={errors.clients}
                label={t("common.client")}
                options={clientOptions}
                name="clients"
                className="w-25"
                isDisabled={isShow}
              />
            </div>

            {/* <TextAreaField
              name="notes"
              control={control}
              placeholder={t("common.note")}
              defaultValue=""
              className="mb-2"
              rows={1}
              disabled={isShow}
              error={errors?.notes}
            /> */}
            <TableContainer
              hideSHowGFilter={true}
              columns={columns || []}
              data={rowData}
              hidePagination
              isSmall
              customComponent={
                <div className="d-flex justify-content-end w-100">
                  <Button
                    className="btn-sm"
                    color="primary"
                    disabled={currentShow}
                    onClick={handelOpenAddModal}
                  >
                    {t("common.add") + " " + t("common.product")}
                  </Button>
                </div>
              }
            />
            <div style={{ display: "flex", marginBottom: 10, gap: 10 }}>
              {totalsFields.map(
                (field) =>
                  field.showIn && (
                    <FormField
                      key={field.id}
                      field={field}
                      register={register}
                      errors={errors}
                      isShow={isShow}
                      t={t}
                      control={control}
                      placeholder={field.label}
                    />
                  )
              )}
              <CustomInput
                control={control}
                name="total_discount_value"
                isDisabled={isShow}
                placeholder={t("bills.discount_value")}
                type="number"
                error={errors?.total_discount_value}
              />
              <CustomInput
                control={control}
                name="total_discount_percentage"
                isDisabled={isShow}
                placeholder={t("bills.discount_percentage")}
                type="number"
                error={errors?.total_discount_percentage}
              />
            </div>
            <div
              style={{
                padding: 10,
                borderTop: "1px solid #ccc",
                marginTop: 20,
                fontSize: 15,
                fontWeight: "bold",
              }}
            >
              {t("common.total")}: {selectedTotalPrice}
            </div>
          </Row>
        </ActionSection>
      </Container>
      <Modal isOpen={openAddModal} backdrop="static">
        <ModalHeader toggle={handelCloseAddModal}>
          {selectedProductId ? t("common.update") : t("common.add")}{" "}
          {t("common.product")}
        </ModalHeader>
        <ModalBody>
          <Row className="g-2">
            {productsFields.map((item) => item.showIn && item.field)}
            <Col xs={12} className="mt-2">
              <TextAreaField
                placeholder={t("common.note")}
                name="product_note"
                control={control}
                defaultValue=""
                className="mb-2"
                disabled={isShow}
              />
            </Col>
          </Row>
          <ModalFooter>
            <Button
              className="btn-sm"
              type="button"
              color="light"
              onClick={handelCloseAddModal}
            >
              {t("common.close")}
            </Button>
            <Button
              type="button"
              className="btn-sm"
              onClick={handelAddProductToList}
              color="primary"
              disabled={
                !watch("product_id") && !watch("quant") && !watch("unit_price")
              }
            >
              {isUpdate ? t("common.update") : t("common.add")}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>

      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCloseAddModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCloseAddModal}>
          {t("common.reject")} {t("common.request")}
        </ModalHeader>
        <ModalBody>
          <Row>
            <p>{t("common.delete_text")}</p>
          </Row>
          <ModalFooter>
            <div className="d-flex align-items-center gap-2">
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCloseAddModal}
              >
                {t("common.close")}
              </Button>
              <Button
                className="btn-sm"
                onClick={rejectStatus}
                type="button"
                color="danger"
              >
                {isRejected ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.reject")
                )}
              </Button>
            </div>
          </ModalFooter>
        </ModalBody>
      </Modal>
      <Modal
        isOpen={opeAcceptModal}
        toggle={handelCloseAddModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCloseAddModal}>
          {t("common.accept")} {t("common.request")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button
              className="btn-sm"
              type="button"
              color="light"
              onClick={handelCLoseModal}
            >
              {t("common.close")}
            </Button>
            <Button
              className="btn-sm"
              onClick={handelAcceptRequest}
              type="button"
              color="success"
            >
              {isAccepted ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.accept")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default OfferActions;
