import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Label,
  Modal,
  ModalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import { Link, useLocation } from "react-router-dom";
import { useStoreRequest } from "../../apis/store-request/query";
import { storeRequestApis } from "../../apis/store-request/api";
import { useForm } from "react-hook-form";
import { handleBackendErrors } from "../../helpers/api_helper";
import ActionStoreRequest from "../../components/storeRequest/actionStoreRequest";
import { Can } from "../../components/permissions-way/can";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfo, FaPenToSquare } from "react-icons/fa6";
import { FaInfoCircle } from "react-icons/fa";

const StoreRequest = () => {
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = useStoreRequest.useGetAll({ limit: 50, page: page });
  const { pathname } = useLocation();
  const [isDeleting, setIsDeleting] = useState(false);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const { t, i18n } = useTranslation();
  const [openAddModal, setOpenAddModal] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [opeAcceptModal, setOpeAcceptModal] = useState(false);
  const [selectId, setSelectId] = useState(null);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("store-request.store_request"),
      link: pathname,
    },
  ];

  const SToreRequestTransaction = {
    1: t("store-request.enter_store"),
    2: t("store-request.out_store"),
    3: t("store-request.enter_car"),
    4: t("store-request.out_car"),
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store-request.created_by"),
      accessor: "created",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.store_operation.store_operation"),
      accessor: "store_operation",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      accessor: (cellProps) => {
        return (
          <>
            {cellProps.status === 1 && (
              <div className="badge bg-warning-subtle text-warning font-size-12">
                {t("types.reason.new")}
              </div>
            )}
            {cellProps.status === 3 && (
              <div className="badge bg-success-subtle text-success font-size-12">
                {t("types.reason.done")}
              </div>
            )}
            {cellProps.status === 2 && (
              <div className="badge bg-danger-subtle text-danger font-size-12">
                {t("common.rejected")}
              </div>
            )}
          </>
        );
      },
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store-request.transaction"),
      accessor: "transaction",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store-request.created_at"),
      accessor: "created_at",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: "notes",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <>
            <Can permission={"store.edit_request"}>
              {cellProps.status === 1 && (
                <Link
                  onClick={() => {
                    setSelectId(cellProps.id);
                    setOpenAddModal(true);
                  }}
                  className="me-3 text-primary"
                >
                  {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                  <FaPenToSquare size={14} />
                </Link>
              )}
            </Can>
            <Can permission={"store.get_requests"}>
              <Link
                // to={`/action-store-request?id=${cellProps.id}?Show=true`}
                onClick={() => {
                  setSelectId(cellProps.id);
                  setOpenAddModal(true);
                  setIsShow(true);
                }}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={18} />
              </Link>
            </Can>
          </>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setOpeAcceptModal(false);
  };

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1,
              store_operation:
                i18n.language === "eng"
                  ? item.store_operation?.title?.en
                  : item.store_operation?.title?.ar || "----",
              created_at: item.created_at,
              created: item?.created?.user || "---",
              status: item.status,
              notes: item.notes,
              transaction: SToreRequestTransaction[item.transaction],
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t, isShow, i18n]
  );

  const { register, watch, reset } = useForm({ defaultValues: { note: "" } });

  const rejectStatus = async () => {
    try {
      setIsDeleting(true);
      const response = await storeRequestApis.reject({
        id: selectId,
        note: watch("note"),
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
      reset();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const handelAcceptRequest = async () => {
    try {
      setIsDeleting(true);
      const response = await storeRequestApis.accept({
        id: selectId,
        note: watch("note"),
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      reset();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
  };

  const handelCloseAddModal = (triggrt) => {
    // setOpenAddModal(false);
    if (triggrt) triggrt();
    setSelectId(0);
    setIsShow(false);
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("store-request.store_request")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("store-request.store_request-add")}
          isAddOptions
          canPermission={"store.make_request"}
          handleOrderClicks={() => setOpenAddModal(true)}
        />
        <Card style={{ height: "95vh", padding: 20 }}>
          {/* <CardBody> */}
          {/* {isLoadingUsers ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
          <TableContainer
            hideSHowGFilter={false}
            columns={columns || []}
            data={rowData || []}
            isPagination={true}
            iscustomPageSize={true}
            isBordered={true}
            isLoading={isLoadingUsers}
            pageSize={10}
            pageIndex={page}
            customHeight={"100%"}
            manualPagination={true}
            pageCount={ContractTypes?.meta?.last_page || 1}
            currentPage={page}
            setPage={setPage}
            // handleOrderClicks={() => navigate("/action-store-request")}
            handleOrderClicks={() => setOpenAddModal(true)}
            customPageSize={10}
            addTitle={t("common.make") + " " + t("store-request.store_request")}
            isAddOptions
            // handelCustomClickOnRow={handelShowRequest}
            className="custom-header-css table align-middle table-nowrap"
            tableClassName="table-centered align-middle table-nowrap mb-0"
            theadClassName="text-muted table-light"
          />
          {/* )} */}
          {/* </CardBody> */}
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.reject")} {t("common.request")}
          </ModalHeader>
          <ModalBody>
            <Row>
              <p>{t("common.delete_text")}</p>
              <Col xs={12}>
                <div className="mb-2">
                  <Label className="form-label" htmlFor="note">
                    {t("store-request.reason_reject")}
                  </Label>
                  <textarea
                    type="text"
                    rows={4}
                    className={`form-control`}
                    placeholder="...."
                    {...register(`note`)}
                  />
                </div>
              </Col>
            </Row>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button onClick={rejectStatus} type="button" color="danger">
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.reject")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
        <Modal
          isOpen={opeAcceptModal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.accept")} {t("common.request")}
          </ModalHeader>
          <ModalBody>
            <Row>
              <p>{t("common.delete_text")}</p>
              <Col xs={12}>
                <div className="mb-2">
                  <Label className="form-label" htmlFor="note">
                    {t("store-request.reason_reject")}
                  </Label>
                  <textarea
                    type="text"
                    rows={4}
                    className={`form-control`}
                    placeholder="...."
                    {...register(`note`)}
                  />
                </div>
              </Col>
            </Row>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button
                onClick={handelAcceptRequest}
                type="button"
                color="success"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.accept")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
        <ActionStoreRequest
          handelCloseAddModal={handelCloseAddModal}
          handelCloseModal={() => {
            handelCloseAddModal();
            setOpenAddModal(false);
          }}
          isStoreRequest
          isShow={isShow}
          openAddModal={openAddModal}
          refetch={refetch}
          selectId={selectId}
          setOpenAddModal={setOpenAddModal}
        />
      </Container>
    </div>
  );
};
export default StoreRequest;
