import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Col, Container, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { productAPis } from "../../apis/products/api"; // Assuming you have a product API
import toastr from "toastr";
import { useTranslation } from "react-i18next";
import { productQueries } from "../../apis/products/query"; // Assuming you have Product Groupss API
import { useLocation, useNavigate } from "react-router-dom";
import { handleBackendErrors } from "../../helpers/api_helper";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import ActionSection from "../../components/Common/ActionSection";
import TextAreaField from "../../components/Common/textArea";
import { ProductsUnitsQueries } from "../../apis/types/product-units/query";
import { ProductTypesQueries } from "../../apis/types/product-types/query";
import ImageUpload from "../../components/Common/ImageUpload.jsx";
import { AuthAPis } from "../../apis/auth/api";

const ProductActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];
  const { t, i18n } = useTranslation();
  const { pathname } = useLocation();
  const { data, isLoading } = productQueries.useGet({
    id: Number(selectId),
  });
  const { data: productTypes } = ProductTypesQueries.useGetAll({
    status: 1,
  });
  const { data: productUnits } = ProductsUnitsQueries.useGetAllProductsUnits(
    {}
  );
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [images, setImages] = useState([]);
  const [icon, setIcon] = useState([]);
  const [productImages, setProductImages] = useState([]);

  const statusOptions = [
    { value: 1, label: t("common.active") },
    { value: 2, label: t("common.in_active") },
  ];

  const navigate = useNavigate();

  const handelCancel = () => {
    navigate("/products");
    reset();
  };

  const schema = yup
    .object({
      name_en: yup.string().required(t("common.field_required")),
      // quant: selectId
      //   ? null
      //   : yup
      //       .number()
      //       .min(1, t("bills.validation.greater_than_0"))
      //       .max(1000000, t("cars.validations.km_max"))
      //       .required(t("common.field_required"))
      //       .typeError(t("common.valid_number")),
      // price: yup
      //   .number()
      //   .min(1, t("bills.validation.greater_than_0"))
      //   .max(1000000, t("cars.validations.km_max"))
      //   .required(t("common.field_required"))
      //   .typeError(t("common.valid_number")),
      // maintaince_quant: yup
      //   .number()
      //   .min(1, t("bills.validation.greater_than_0"))
      //   .max(1000000, t("cars.validations.km_max"))
      //   .required(t("common.field_required"))
      //   .typeError(t("common.valid_number")),
      // maintaince_quant: selectId
      //   ? null
      //   : yup.number().min(1).required(t("common.field_required")),
      notification_minimum_quantity: yup
        .number()
        .min(1, t("bills.validation.greater_than_0"))
        .max(1000000, t("cars.validations.km_max"))
        .required(t("common.field_required"))
        .typeError(t("common.valid_number")),
      product_type_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
      product_unit_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
    })
    .required();

  const initialSTate = {
    name_en: "",
    name_ar: "",
    description_en: "",
    description_ar: "",
    div_quant: 1,
    default_add_quant: null,
    max_quant: null,
    notification_minimum_quantity: null,
    quant: null,
    maintaince_quant: null,
    price: null,
    product_type_id: null,
    product_unit_id: null,
    status: statusOptions[0],
    icon: null, // Add image default value
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      reset({
        name_en: data?.result.name,
        description_en: data?.result.description,
        div_quant: data?.result.div_quant,
        default_add_quant: data?.result.default_add_quant,
        max_quant: data?.result.max_quant,
        notification_minimum_quantity:
          data?.result.notification_minimum_quantity,
        quant: data?.result.quant,
        maintaince_quant: data?.result.maintaince_quant,
        price: data?.result.price,
        product_type_id: {
          value: data?.result.product_type.id,
          label:
            i18n.language === "ar"
              ? data?.result.product_type.title.ar
              : data?.result.product_type.title.en || "",
        },
        product_unit_id: {
          value: data?.result.product_unit.id,
          label:
            i18n.language === "ar"
              ? data?.result.product_unit.title.ar
              : data?.result.product_unit.title.en || "",
        },
        status: {
          value: data?.result.status,
          label: statusOptions.find(
            (item) => item.value === data?.result?.status
          )?.label,
        },
        icon: null, // Add image default value
      });
      // Set the icon if it exists in the response
      if (data?.result?.image) {
        setIcon([
          {
            data_url: data.result?.image?.url,
            id: data.result?.image?.id,
          },
        ]);
      }
      // Set product images if they exist
      if (data?.result?.images?.length > 0) {
        setProductImages(
          data.result.images.map((img) => ({
            data_url: img.url,
            id: img.id,
          }))
        );
      }
    }
  }, [selectId, isLoading, data, productTypes, productUnits, reset]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // const appendIfNotNull = (key, value, formData) => {
  //   if (value !== null && value !== undefined && value !== "") {
  //     formData.append(key, value);
  //   }
  // };

  const handleIconChange = (imageList) => {
    setIcon(imageList);
  };

  const handleProductImagesChange = (imageList) => {
    setProductImages(imageList);
  };

  const updateProduct = async (data) => {
    // Create JSON data for the update
    const jsonData = {
      name: data.name_en,
      description: data.description_en || " ",
      div_quant: data.div_quant,
      default_add_quant: data.default_add_quant,
      max_quant: data.max_quant,
      notification_minimum_quantity: data.notification_minimum_quantity,
      quant: data.quant || 0,
      maintaince_quant: data.maintaince_quant || 0,
      price: data.price || 0,
      product_type_id: data.product_type_id.value,
      product_unit_id: data.product_unit_id.value,
      status: data.status.value,
    };

    try {
      const formData = new FormData();

      // Add icon if exists
      if (icon?.length > 0 && icon[0]?.file) {
        formData.append("icon", icon[0].file);
      }

      // Add product images if they exist
      productImages.forEach((image, index) => {
        if (image.file) {
          formData.append(`images[${index}]`, image.file);
        }
      });

      // Add all other fields to formData
      Object.keys(jsonData).forEach((key) => {
        formData.append(key, jsonData[key]);
      });

      await productAPis.update({ formData: formData, id: selectId });
      toastr.success("Product updated successfully");
      navigate("/products");
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addProduct = async (data) => {
    const jsonData = {
      name: data.name_en,
      description: data.description_en || " ",
      div_quant: data.div_quant,
      default_add_quant: data.default_add_quant,
      max_quant: data.max_quant,
      notification_minimum_quantity: data.notification_minimum_quantity,
      quant: data.quant || 0,
      maintaince_quant: data.maintaince_quant || 0,
      price: data.price || 0,
      product_type_id: data.product_type_id.value,
      product_unit_id: data.product_unit_id.value,
      status: data.status.value,
    };

    try {
      const formData = new FormData();

      // Add icon if exists
      if (icon?.length > 0 && icon[0]?.file) {
        formData.append("icon", icon[0].file);
      }

      // Add product images if they exist
      productImages.forEach((image, index) => {
        if (image.file) {
          formData.append(`images[${index}]`, image.file);
        }
      });

      // Add all other fields to formData
      Object.keys(jsonData).forEach((key) => {
        formData.append(key, jsonData[key]);
      });

      await productAPis.add(formData);
      toastr.success("Product added successfully");
      navigate("/products");
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const breadcrumbItems = [
    { title: t("products.products"), link: "/products" },
    {
      title: isShow
        ? t("common.show")
        : selectId
        ? t("common.update")
        : t("common.create"),
      link: pathname,
    },
  ];

  // Prepare options for select inputs
  const productTypeOptions =
    productTypes?.result?.map((item) => ({
      value: item.id,
      label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
    })) || [];

  const productUnitOptions =
    productUnits?.result?.map((item) => ({
      value: item.id,
      label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
    })) || [];

  const handleImageDelete = async (imageId) => {
    try {
      setIsImageLoading(true);
      const response = await AuthAPis.deleteImage({ id: imageId });
      toastr.success(response.message);
      setImages([]);
    } catch (error) {
      handleBackendErrors({ error, setError });
    } finally {
      setIsImageLoading(false);
    }
  };

  const handleImageChange = (imageList) => {
    setImages(imageList);
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("products.products")}
          breadcrumbItems={breadcrumbItems}
          currentPageLink={"/action-products"}
          titleLink={"/products"}
          titleOfPage={
            isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
          titleOfSection={t("products.products")}
        />
        <ActionSection
          handleCancel={handelCancel}
          isSubmitting={isSubmitting}
          isEdit={!!selectId}
          isView={!!isShow}
          UpdateFun={updateProduct}
          isLoading={isLoading}
          handleSubmit={handleSubmit}
          handelCancel={handelCancel}
          addFun={addProduct}
          selectId={selectId}
          isShow={!!isShow}
        >
          <Row>
            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="name_en"
                  placeholder={t("products.product_name")}
                  type="text"
                  isDisabled={isShow}
                  error={errors.name_en}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="price"
                  placeholder={t("common.price")}
                  type="number"
                  isDisabled={isShow}
                  error={errors.price}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="quant"
                  placeholder={t("products.start_quant")}
                  type="number"
                  isDisabled={isShow}
                  error={errors.quant}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="maintaince_quant"
                  placeholder={t("common.maintenance_quantity")}
                  type="number"
                  isDisabled={isShow}
                  error={errors.maintaince_quant}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="notification_minimum_quantity"
                  placeholder={t("products.min_quantity")}
                  type="number"
                  isDisabled={isShow}
                  error={errors.notification_minimum_quantity}
                />
              </div>
            </Col>
            {/* 

            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="max_quant"
                  placeholder={t("products.max_quantity")}
                  type="number"
                  isDisabled={isShow}
                  error={errors.max_quant}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="default_add_quant"
                  placeholder={t("products.default_add_quantity")}
                  type="number"
                  isDisabled={isShow}
                  error={errors.default_add_quant}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomInput
                  control={control}
                  name="div_quant"
                  placeholder={t("products.div_quant")}
                  type="number"
                  isDisabled={isShow}
                  error={errors.div_quant}
                />
              </div>
            </Col> */}

            <Col xs={3}>
              <div className="mb-3">
                <CustomSelect
                  name="status"
                  control={control}
                  options={statusOptions}
                  placeholder={t("common.status")}
                  isDisabled={isShow}
                  error={errors.status}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomSelect
                  name="product_type_id"
                  control={control}
                  options={productTypeOptions}
                  placeholder={t("products.product_group")}
                  isDisabled={isShow}
                  error={errors.product_type_id}
                />
              </div>
            </Col>

            <Col xs={3}>
              <div className="mb-3">
                <CustomSelect
                  name="product_unit_id"
                  control={control}
                  options={productUnitOptions}
                  placeholder={t("products.product_unit")}
                  isDisabled={isShow}
                  error={errors.product_unit_id}
                />
              </div>
            </Col>

            <Col xs={12}>
              <div className="mb-3">
                <TextAreaField
                  name="description_en"
                  register={register}
                  placeholder={t("common.description")}
                  // defaultValue={data?.result?.description}
                  className="mb-2"
                  disabled={isShow}
                  error={errors?.description_en}
                  rows={4}
                />
              </div>
            </Col>
            <Col xs={12} className="mb-2">
              <ImageUpload
                images={icon}
                onChange={handleIconChange}
                maxNumber={1}
                isMultiple={false}
                isDisabled={isShow}
                onError={(error) => handleBackendErrors({ error, setError })}
                label={t("products.icon")}
              />
            </Col>

            <Col xs={12} className="mb-2">
              <ImageUpload
                images={productImages}
                onChange={handleProductImagesChange}
                maxNumber={6}
                isMultiple={true}
                isDisabled={isShow}
                onError={(error) => handleBackendErrors({ error, setError })}
                label={t("products.product_images")}
              />
            </Col>
          </Row>
        </ActionSection>
      </Container>
    </div>
  );
};

export default ProductActions;
