import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Input,
  Modal,
  ModalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>,
} from "reactstrap";
import * as Yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import TableContainer from "../../components/Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import { delegateQueries } from "../../apis/delegate/query";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { delegateAPis } from "../../apis/delegate/api";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { DelegateCarsEnum, DelegateCarsStatus } from "../../constant/constants";
import { handleBackendErrors, hasPermission } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import CustomSelect from "../../components/Common/Select";
import CustomInput from "../../components/Common/Input";
import SearchCard from "../../components/Reports/search-card";
import TypesModel from "../../components/Common/types-model";
import ActionDelegate from "./action-delegate";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const Delegate = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useState({});
  const [pagination, setPagination] = useState(1);
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const { isLoading, data, refetch } = delegateQueries.useGetAll({
    limit: 6,
    page: pagination,
    searchParams,
    enabled: true,
  });

  const [open, setOpen] = useState(false);

  const handelCloseSideBar = () => {
    setOpen(false);
  };
  const [selectId, setSelectId] = useState(null);
  const [isShow, setIsShow] = useState(false);
  const [pageIndex, setPageIndex] = useState(0);
  const [selectedStatus, setSelectIdStatus] = useState("");
  const [selectedCarId, setSelectedCarId] = useState(0);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [isSending, setIsSending] = useState(0);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [openAssignModal, setOpenAssignCarModal] = useState(false);
  const [openAssignClientsGroupModal, setOpenAssignClientsGroupModal] =
    useState(false);
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [optionClientGroup, setoptionClientGroup] = useState([]); // Assuming you have a way to populate this

  const { data: carsList, refetch: refetchCarList } =
    delegateQueries.useGetAvailabel({
      enabled: openAssignModal && !!selectId,
    });
  const { data: clientsGroupList } =
    delegateQueries.useGetAvailabelCLientsGroup({
      enabled: openAssignModal && !!selectId,
    });

  const handelOpenAssignModal = () => {
    setOpenAssignCarModal(true);
  };
  const handelCloseAssignModal = () => {
    setOpenAssignCarModal(false);
  };
  const handelOpenAssignClientsGroupModal = () => {
    setOpenAssignClientsGroupModal(true);
  };
  const handelCloseAssignClientsGroupModal = () => {
    setOpenAssignClientsGroupModal(false);
  };

  const validationSchema = Yup.object().shape({
    status: Yup.string().required(t("delegate.validation.status_required")),
    km: Yup.number().when("status", {
      is: "With Car",
      then: Yup.number()
        .min(1, t("delegate.validation.km_min"))
        .required(t("delegate.validation.km_required")),
    }),
    cars: Yup.array().when("status", {
      is: "Without Car",
      then: Yup.array()
        .min(1, t("delegate.validation.cars_min"))
        .required(t("delegate.validation.cars_required")),
    }),
  });

  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    control,
    reset,
    register,
    watch,
    setError,
    clearErrors,
    setValue,
    getValues,
  } = useForm({
    defaultValues: {
      cars: null,
      clientsGroups: [],
      km: "",
      full_name: "",
      status: null,
      car: null,
    },
    resolver: yupResolver(validationSchema),
  });

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setIsShow(false);
    setOpen(false);
    setOpenStatsusModal(false);
    handelCloseAssignModal();
    reset();
    handelCloseAssignClientsGroupModal();
    setSelectIdStatus("");
    setSelectedCarId(0);
    refetch();
  };

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };

  const [isDeleting, setIsDeleting] = useState(false);

  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    // navigate("/action-delegate");
    setOpen(true);
  };

  useEffect(() => {
    if (carsList?.data?.length > 0) {
      // Reset the options first, then add the new cars to prevent duplicates
      setOptionGroup(() =>
        carsList?.data?.map((item) => ({
          label: item.name,
          value: item.id,
          km: item.last_km,
        }))
      );
    }
  }, [carsList?.data?.length]);

  useEffect(() => {
    if (clientsGroupList?.data?.length > 0) {
      setoptionClientGroup((prev) => [
        ...prev,
        ...clientsGroupList?.data?.map((item) => ({
          label: item.name,
          value: item.id,
        })),
      ]);
    }
  }, [clientsGroupList?.data?.length]);

  // Status options for filtering
  const statusOptions = [
    {
      label: t("common.active"),
      value: 1,
    },
    {
      label: t("common.in_active"),
      value: 2,
    },
  ];

  // Filter fields configuration for SearchCard
  const SearchData = [
    {
      id: 2,
      label: t("common.status"),
      type: "select",
      name: "status",
      options: statusOptions,
      isMulti: false,
      cols: 2,
      component: CustomFilterSearch,
      handleSearch: () => handleSearch(getValues()),
    },
  ];

  // Input fields configuration for SearchCard
  const inputsArray = [
    {
      id: 3,
      name: "full_name",
      type: "text",
      label: t("common.full_name"),
      cols: 2,
    },
  ];

  // Function to update URL with search parameters
  const updateUrlWithFilters = (params) => {
    const newUrl = new URLSearchParams();

    // Full name search
    if (params["filter[full_name]"]) {
      newUrl.set("full_name", params["filter[full_name]"]);
    }

    // Status filter
    if (params["filter[status]"] !== undefined) {
      newUrl.set("status", params["filter[status]"]);
    }

    // Replace current URL without reloading the page
    navigate(`${pathname}?${newUrl.toString()}`, { replace: true });
  };

  // Function to parse URL parameters
  // const parseUrlParams = () => {
  //   try {
  //     const params = {};
  //     const initialFormValues = {
  //       full_name: "",
  //       status: null,
  //     };

  //     // Parse full name
  //     const fullName = queryParams.get("full_name");
  //     if (fullName) {
  //       params["filter[full_name]"] = fullName;
  //       initialFormValues.full_name = fullName;
  //     }

  //     // Parse status
  //     const status = queryParams.get("status");
  //     if (status !== null && statusOptions.length > 0) {
  //       params["filter[status]"] = status;
  //       initialFormValues.status = statusOptions.find(
  //         (option) => option.value?.toString() === status
  //       );
  //     }

  //     return { params, initialFormValues };
  //   } catch (error) {
  //     console.error("Error parsing URL parameters:", error);
  //     return {
  //       params: {},
  //       initialFormValues: {
  //         full_name: "",
  //         status: null,
  //       },
  //     };
  //   }
  // };

  const searchFields = watch(["status", "full_name"]);

  // Add a manual search function instead of relying on the useEffect
  const handleSearch = () => {
    const params = {};

    // Check if delegates multi-select has values
    if (searchFields[0]) {
      // Handle both single object and array of objects
      if (Array.isArray(searchFields[0])) {
        if (searchFields[0].length > 0) {
          // Create separate parameters for each delegate ID
          searchFields[0].forEach((delegate) => {
            if (!params["filter[status]"]) {
              params["filter[status]"] = [delegate.value];
            } else {
              params["filter[status]"].push(delegate.value);
            }
          });
        }
      } else if (searchFields[0].value) {
        // Single object case
        params["filter[status]"] = [searchFields[0].value];
      }
    }

    if (searchFields[1]) {
      params["filter[full_name]"] = searchFields[1];
    }

    // Update search params
    setSearchParams(params);
    // Update URL with new filters
    updateUrlWithFilters(params);
    // Reset to first page
    setPagination(1);
    // Trigger a refetch with new parameters
    refetch();
  };

  // Update handleReset to also clear URL
  const handleReset = () => {
    // Reset form fields
    reset({
      full_name: "",
      car: null,
      status: null,
      clientsGroups: [],
      km: "",
    });

    // Clear search params
    setSearchParams({});
    // Clear URL params
    navigate(pathname, { replace: true });
    setPagination(1);
  };

  // // Initialize from URL on first load
  // useEffect(() => {
  //   if (statusOptions.length > 0) {
  //     const { params, initialFormValues } = parseUrlParams();

  //     // Set form values from URL
  //     reset(initialFormValues);

  //     // If we have params from URL, set them
  //     if (Object.keys(params).length > 0) {
  //       setSearchParams(params);
  //     }
  //   }
  // }, [statusOptions.length, queryParams]);

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await delegateAPis.active({
        id: id,
      });
      refetch();
      // toastr.success("Active done ");
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await delegateAPis.inActive({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const handelAssignCar = async (selectedStatusToSend) => {
    // الحصول على المفتاح من القيمة في الـ enum
    const getKeyByValue = (enumObj, value) => {
      return Object.keys(enumObj).find((key) => enumObj[key] === value);
    };

    clearErrors();
    if (selectedStatus === "With Car" && Number(watch("km")) <= 0) {
      toastr.error(t("delegate.validation.km_requeued"));
      return;
    } else if (
      selectedStatusToSend === "With Car" &&
      (Number(watch("km")) <= 0 || !watch("cars")?.value)
    ) {
      toastr.error(t("delegate.validation.assign_validation"));
      return;
    }

    // الحصول على المفتاح من DelegateCarsEnum
    const statusKey = getKeyByValue(DelegateCarsEnum, selectedStatusToSend);

    if (!statusKey) {
      console.error("Invalid status value:", selectedStatusToSend);
      toastr.error("Invalid status selected");
      return;
    }

    const dataToSend = {
      car_id:
        selectedStatus === "Un Active"
          ? watch("cars")?.value
          : selectedStatus === "With Car"
          ? selectedCarId
          : watch("cars")?.value,
      km: watch("km"),
      system_status: Number(statusKey), // تحويل المفتاح إلى رقم
    };
    try {
      setIsDeleting(true);
      setIsSending(Number(dataToSend.system_status));
      const response = await delegateAPis.assignCar({
        cardId: dataToSend.car_id,
        delegateId: selectId,
        system_status: dataToSend.system_status,
        km: watch("km"),
      });
      refetchCarList();
      setIsDeleting(false);
      handelCLoseModal();
      refetch();
      handelCLoseModal();
      setIsSending(0);
      toastr.success(response.message);
    } catch (error) {
      setIsSending(0);
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
      console.error("Error:", error);
    }
  };

  const handelAssignAvailableGroup = async (data) => {
    try {
      const flterIds = data.clientsGroups.map((item) => item.value);
      setIsDeleting(true);
      await delegateAPis.assignCLientGroup({
        clinent_group_id: flterIds,
        delegate_id: selectId,
      });
      setIsDeleting(false);
      refetch();
      toastr.success("Assign client group done");
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      console.log("error");
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
    }
  };

  const breadcrumbItems = [
    {
      title: t("common.delegate"),
      link: pathname,
    },
  ];
  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1 && hasPermission("delegate.disactivate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
    } else if (hasPermission("delegate.activate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
    }
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: t("common.image"),
    //   accessor: "image",
    //   disableFilters: true,
    //   filterable: false,
    // },
    {
      Header: t("common.full_name"),
      accessor: "full_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("delegate.clients_count"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div
            style={{ cursor: "pointer" }}
            onClick={() => {
              if (hasPermission("delegate.assign_client_group")) {
                handelOpenAssignClientsGroupModal();
                setSelectId(cellProps.id);
              }
            }}
          >
            <p>{cellProps.clients_count}</p>
          </div>
        );
      },
    },
    // {
    //   Header: t("common.email"),
    //   accessor: "email",
    //   disableFilters: true,
    //   filterable: false,
    // },
    {
      Header: t("common.phone_number"),
      accessor: "phone_number",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.car"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div
            style={{
              cursor: "pointer",
            }}
            onClick={() => {
              if (hasPermission("delegate.assign_car")) {
                handelOpenAssignModal();
                setSelectId(cellProps.id);
                const reverseStatus = Object.fromEntries(
                  Object.entries(DelegateCarsStatus(t)).map(([key, value]) => [
                    value,
                    key,
                  ])
                );
                // Example usage
                const getValueKey = (value) =>
                  reverseStatus[value] || "Key not found";
                const getValueFromName = Number(
                  getValueKey(cellProps.system_status)
                );
                setSelectIdStatus(DelegateCarsEnum[getValueFromName]);
                if (cellProps.car_id) setSelectedCarId(cellProps.car_id);
              }
            }}
          >
            <span>{cellProps.car}</span>
          </div>
        );
      },
    },
    {
      Header: t("delegate.delegate_car_status"),
      accessor: "system_status",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div className="form-check form-switch">
            <Input
              type="checkbox"
              className="form-check-input"
              checked={cellProps.status === 1}
              onClick={() => handelToggleStatus({ cellProps })}
            />
          </div>
        );
      },
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"delegate.update"}>
              <Link
                // to={
                //   cellProps.isDefault !== 1 &&
                //   `/action-delegate?id=${cellProps.id}`
                // }
                className="text-primary"
                onClick={() => {
                  setSelectId(cellProps.id);
                  setOpen(true);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"delegate.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            <Can permission={"delegate.show"}>
              <Link
                // to={`/action-delegate?id=${cellProps.id}?Show=true`}
                className="text-success"
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    // handelOpenModal();
                    handelSelectId(cellProps.id);
                    setIsShow(true);
                    setOpen(true);
                  }
                }}
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (pagination - 1) * 10 + index + 1, // 10 is your page size
              full_name: item.full_name || "----",
              clients_count: item.clients_count || "----",
              email: item.email || "----",
              image: item.image || "----",
              car: item.car?.name || "----",
              car_id: item.car?.id || "----",
              status: item.status,
              phone_number: item.phone_number, // Keep the original ID if needed
              system_status: DelegateCarsStatus(t)[item.system_status], // Keep the original ID if needed
            }))
            .reverse()
        : [],
    [data?.result, t]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await delegateAPis.deleteFu({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("common.delegate")}
          breadcrumbItems={breadcrumbItems}
          canPermission={"delegate.store"}
          handleOrderClicks={handelAddBonds}
          disabledAddTitle={false}
          addTitle={t("common.add") + " " + t("common.delegate")}
          isAddOptions={true}
        />
        <Card style={{ maxHeight: "90%", height: "90%", overflowY: "auto" }}>
          <CardBody>
            {/* {isLoading ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              addTitle={t("common.add") + " " + t("common.delegate")}
              isLoading={isLoading}
              pageIndex={pageIndex}
              customComponent={
                <SearchCard
                  SearchData={SearchData}
                  control={control}
                  hadelReset={handleReset}
                  inputsArray={inputsArray}
                  register={register}
                  handelSearch={handleSearch}
                  watch={watch}
                  setValue={setValue}
                />
              }
              customPageSize={10}
              pageCount={data?.meta?.last_page || 1}
              currentPage={pagination}
              setPage={setPagination}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
            {/* )} */}
          </CardBody>
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("common.delegate")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.close")}
              </Button>
              <Button
                className="btn-sm"
                onClick={DeleteFun}
                type="button"
                color="danger"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.no")}
              </Button>
              <Button
                className="btn-sm"
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                disabled={isDeleting}
                type="button"
                color="primary"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}
      {openAssignModal && selectId && (
        <Modal isOpen={openAssignModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("delegate.assign_car")}
          </ModalHeader>
          <ModalBody>
            <form onSubmit={(e) => e.preventDefault()}>
              <Row className="g-3">
                {selectedStatus !== "With Car" && (
                  <Col xs={12}>
                    <CustomSelect
                      name="cars"
                      control={control}
                      error={errors.cars}
                      options={optionGroup}
                      label={t("delegate.available_cars")}
                      isMulti={false}
                      menuHeight={100}
                    />
                  </Col>
                )}
                <Col xs={12}>
                  <CustomInput
                    name="km"
                    control={control}
                    error={errors.km}
                    type="number"
                    label={t("common.km")}
                  />
                </Col>
              </Row>
              <ModalFooter>
                {selectedStatus === "With Car" && (
                  <>
                    <Button
                      className="btn-sm"
                      type="button"
                      color="light"
                      onClick={() => {
                        handelAssignCar(DelegateCarsEnum[1]);
                      }}
                    >
                      {isSending === 1 ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("delegate.un_active")
                      )}
                    </Button>
                    <Button
                      className="btn-sm"
                      disabled={isDeleting}
                      color="primary"
                      type="submit"
                      onClick={() => {
                        handelAssignCar(DelegateCarsEnum[3]);
                      }}
                    >
                      {isSending === 3 ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("delegate.without_car")
                      )}
                    </Button>
                  </>
                )}
                {selectedStatus === "Without car" && (
                  <>
                    <Button
                      className="btn-sm"
                      type="button"
                      color="light"
                      onClick={() => {
                        handelAssignCar(DelegateCarsEnum[1]);
                      }}
                    >
                      {isSending === 1 ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("delegate.un_active")
                      )}
                    </Button>
                    <Button
                      className="btn-sm"
                      disabled={isDeleting}
                      type="button"
                      onClick={() => {
                        handelAssignCar(DelegateCarsEnum[2]);
                      }}
                      color="primary"
                    >
                      {isSending === 2 ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("common.save")
                      )}
                    </Button>
                  </>
                )}
                {selectedStatus === "Un Active" && (
                  <>
                    <Button
                      className="btn-sm"
                      type="button"
                      color="light"
                      onClick={() => {
                        handelAssignCar(DelegateCarsEnum[3]);
                      }}
                    >
                      {isSending === 3 ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("delegate.without_car")
                      )}
                    </Button>
                    <Button
                      className="btn-sm"
                      disabled={isDeleting}
                      type="button"
                      onClick={() => {
                        handelAssignCar(DelegateCarsEnum[2]);
                      }}
                      color="primary"
                    >
                      {isSending === 2 ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("common.save")
                      )}
                    </Button>
                  </>
                )}
              </ModalFooter>
            </form>
          </ModalBody>
        </Modal>
      )}
      {openAssignClientsGroupModal && selectId && (
        <Modal isOpen={openAssignClientsGroupModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("delegate.assign_group")}
          </ModalHeader>
          <ModalBody>
            <form onSubmit={handleSubmit(handelAssignAvailableGroup)}>
              <Row className="g-3">
                <Col xs={12}>
                  <CustomSelect
                    name="clientsGroups"
                    control={control}
                    error={errors.clientsGroups}
                    options={optionClientGroup}
                    label={t("delegate.available_group")}
                    isMulti={true}
                    menuHeight={100}
                  />
                </Col>
              </Row>
              <ModalFooter>
                <Button
                  className="btn-sm"
                  type="button"
                  color="light"
                  onClick={handelCLoseModal}
                >
                  {t("common.no")}
                </Button>
                <Button
                  className="btn-sm"
                  disabled={isSubmitting}
                  type="submit"
                  color="primary"
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.yes")
                  )}
                </Button>
              </ModalFooter>
            </form>
          </ModalBody>
        </Modal>
      )}

      <TypesModel
        open={open}
        handelClose={handelCloseSideBar}
        hideAll={true}
        content={
          <div>
            <h1 style={{ fontSize: 16 }} className="mb-4">
              {isShow
                ? t("common.show") + " " + t("common.delegate")
                : selectId
                ? t("common.update") + " " + t("common.delegate")
                : t("common.add") + " " + t("common.delegate")}
            </h1>
            <ActionDelegate
              handelClose={handelCLoseModal}
              isShow={isShow}
              // selectedId={selectId}
              selectId={selectId}
            />
          </div>
        }
      />
    </div>
  );
};
export default Delegate;
